/**
 * CRM系统布局相关JavaScript
 * 负责侧边栏、导航菜单等布局交互功能
 */

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeLayout();
    initializeSidebar();
    initializeMenus();
});

/**
 * 初始化布局
 */
function initializeLayout() {
    // 检查屏幕尺寸，自动折叠侧边栏
    if (window.innerWidth <= 768) {
        const sidebar = document.getElementById('sidebar');
        if (sidebar) {
            sidebar.classList.add('collapsed');
        }
    }
    
    // 监听窗口大小变化
    window.addEventListener('resize', handleWindowResize);
}

/**
 * 初始化侧边栏
 */
function initializeSidebar() {
    const sidebarToggle = document.getElementById('sidebarToggle');
    const sidebar = document.getElementById('sidebar');
    
    if (sidebarToggle && sidebar) {
        sidebarToggle.addEventListener('click', function() {
            toggleSidebar();
        });
    }
    
    // 初始化子菜单
    initializeSubmenus();
}

/**
 * 切换侧边栏展开/折叠状态
 */
function toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    if (sidebar) {
        sidebar.classList.toggle('collapsed');
        
        // 保存状态到localStorage
        const isCollapsed = sidebar.classList.contains('collapsed');
        localStorage.setItem('sidebarCollapsed', isCollapsed);
        
        // 触发自定义事件
        window.dispatchEvent(new CustomEvent('sidebarToggle', {
            detail: { collapsed: isCollapsed }
        }));
    }
}

/**
 * 初始化子菜单
 */
function initializeSubmenus() {
    const submenuItems = document.querySelectorAll('.has-submenu');
    
    submenuItems.forEach(item => {
        const link = item.querySelector('.nav-link');
        if (link) {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                toggleSubmenu(item);
            });
        }
    });
    
    // 默认展开包含当前活动页面的子菜单
    const activeSubmenuLink = document.querySelector('.submenu-link.active');
    if (activeSubmenuLink) {
        const parentSubmenu = activeSubmenuLink.closest('.has-submenu');
        if (parentSubmenu) {
            parentSubmenu.classList.add('expanded');
        }
    }
}

/**
 * 切换子菜单展开/折叠状态
 */
function toggleSubmenu(submenuItem) {
    const isExpanded = submenuItem.classList.contains('expanded');
    
    // 先折叠所有其他子菜单
    document.querySelectorAll('.has-submenu.expanded').forEach(item => {
        if (item !== submenuItem) {
            item.classList.remove('expanded');
        }
    });
    
    // 切换当前子菜单状态
    if (isExpanded) {
        submenuItem.classList.remove('expanded');
    } else {
        submenuItem.classList.add('expanded');
    }
}

/**
 * 初始化菜单项
 */
function initializeMenus() {
    // 为所有导航链接添加点击事件
    const navLinks = document.querySelectorAll('.nav-link:not(.has-submenu .nav-link)');
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            // 如果是实际的链接，不阻止默认行为
            if (this.getAttribute('href') !== '#') {
                return;
            }
            
            e.preventDefault();
            setActiveNavItem(this);
        });
    });
    
    // 为子菜单链接添加点击事件
    const submenuLinks = document.querySelectorAll('.submenu-link');
    submenuLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            if (this.getAttribute('href') !== '#') {
                return;
            }
            
            e.preventDefault();
            setActiveSubmenuItem(this);
        });
    });
}

/**
 * 设置活动的导航项
 */
function setActiveNavItem(clickedLink) {
    // 移除所有导航项的活动状态
    document.querySelectorAll('.nav-link').forEach(link => {
        link.classList.remove('active');
    });
    
    // 移除所有子菜单项的活动状态
    document.querySelectorAll('.submenu-link').forEach(link => {
        link.classList.remove('active');
    });
    
    // 设置点击的导航项为活动状态
    clickedLink.classList.add('active');
    
    // 更新面包屑导航
    updateBreadcrumb(clickedLink);
}

/**
 * 设置活动的子菜单项
 */
function setActiveSubmenuItem(clickedLink) {
    // 移除所有子菜单项的活动状态
    document.querySelectorAll('.submenu-link').forEach(link => {
        link.classList.remove('active');
    });
    
    // 设置点击的子菜单项为活动状态
    clickedLink.classList.add('active');
    
    // 设置父菜单项为活动状态
    const parentNavLink = clickedLink.closest('.has-submenu').querySelector('.nav-link');
    if (parentNavLink) {
        document.querySelectorAll('.nav-link').forEach(link => {
            link.classList.remove('active');
        });
        parentNavLink.classList.add('active');
    }
    
    // 更新面包屑导航
    updateBreadcrumb(clickedLink);
}

/**
 * 更新面包屑导航
 */
function updateBreadcrumb(activeLink) {
    const breadcrumb = document.querySelector('.breadcrumb');
    if (!breadcrumb) return;
    
    const linkText = activeLink.textContent.trim();
    const isSubmenuLink = activeLink.classList.contains('submenu-link');
    
    let breadcrumbHTML = `
        <span class="breadcrumb-item">
            <a href="#">首页</a>
        </span>
        <span class="breadcrumb-separator">></span>
    `;
    
    if (isSubmenuLink) {
        const parentText = activeLink.closest('.has-submenu').querySelector('.nav-text').textContent.trim();
        breadcrumbHTML += `
            <span class="breadcrumb-item">
                <a href="#">${parentText}</a>
            </span>
            <span class="breadcrumb-separator">></span>
        `;
    }
    
    breadcrumbHTML += `
        <span class="breadcrumb-item current">${linkText}</span>
    `;
    
    breadcrumb.innerHTML = breadcrumbHTML;
}

/**
 * 处理窗口大小变化
 */
function handleWindowResize() {
    const sidebar = document.getElementById('sidebar');
    if (!sidebar) return;
    
    if (window.innerWidth <= 768) {
        // 小屏幕自动折叠侧边栏
        if (!sidebar.classList.contains('collapsed')) {
            sidebar.classList.add('collapsed');
        }
    } else {
        // 大屏幕恢复之前的状态
        const savedState = localStorage.getItem('sidebarCollapsed');
        if (savedState === 'false') {
            sidebar.classList.remove('collapsed');
        }
    }
}

/**
 * 获取当前活动的菜单项
 */
function getCurrentActiveMenu() {
    const activeNavLink = document.querySelector('.nav-link.active');
    const activeSubmenuLink = document.querySelector('.submenu-link.active');
    
    return {
        navLink: activeNavLink,
        submenuLink: activeSubmenuLink
    };
}

/**
 * 设置页面标题
 */
function setPageTitle(title, subtitle = '') {
    const pageTitle = document.querySelector('.page-title');
    const pageSubtitle = document.querySelector('.page-subtitle');
    
    if (pageTitle) {
        // 保留图标，只更新文本
        const icon = pageTitle.querySelector('span') || '';
        pageTitle.innerHTML = `${icon ? icon.outerHTML : ''}${title}`;
    }
    
    if (pageSubtitle && subtitle) {
        pageSubtitle.textContent = subtitle;
    }
}

/**
 * 显示加载状态
 */
function showPageLoading() {
    const mainContent = document.querySelector('.main-content');
    if (mainContent) {
        const loadingOverlay = document.createElement('div');
        loadingOverlay.className = 'loading-overlay';
        loadingOverlay.innerHTML = `
            <div class="loading-spinner">
                <div class="spinner"></div>
                <div class="loading-text">正在加载...</div>
            </div>
        `;
        mainContent.appendChild(loadingOverlay);
    }
}

/**
 * 隐藏加载状态
 */
function hidePageLoading() {
    const loadingOverlay = document.querySelector('.loading-overlay');
    if (loadingOverlay) {
        loadingOverlay.remove();
    }
}

/**
 * 显示消息提示
 */
function showMessage(message, type = 'info') {
    const messageContainer = document.createElement('div');
    messageContainer.className = `message-toast message-${type}`;
    messageContainer.textContent = message;
    
    document.body.appendChild(messageContainer);
    
    // 自动移除消息
    setTimeout(() => {
        messageContainer.remove();
    }, 3000);
}

// 导出函数供其他模块使用
window.CRMLayout = {
    toggleSidebar,
    setActiveNavItem,
    setActiveSubmenuItem,
    updateBreadcrumb,
    setPageTitle,
    showPageLoading,
    hidePageLoading,
    showMessage,
    getCurrentActiveMenu
};
