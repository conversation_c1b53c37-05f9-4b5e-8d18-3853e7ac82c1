/* CRM系统整体布局样式 */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: "Microsoft YaHei", "PingFang SC", "Helvetica Neue", Arial, sans-serif;
    font-size: 14px;
    line-height: 1.5;
    color: #333;
    background-color: #f5f7fa;
    overflow-x: hidden;
}

/* 顶部导航栏 */
.top-navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 60px;
    background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
}

.navbar-left {
    display: flex;
    align-items: center;
}

.logo-section {
    display: flex;
    align-items: center;
    color: white;
}

.logo-icon {
    width: 28px;
    height: 28px;
    margin-right: 12px;
}

.system-title {
    font-size: 18px;
    font-weight: 600;
    letter-spacing: 0.5px;
}

.navbar-center {
    display: flex;
    align-items: center;
    gap: 30px;
}

.nav-item {
    color: rgba(255, 255, 255, 0.9);
    text-decoration: none;
    padding: 8px 16px;
    border-radius: 4px;
    transition: all 0.3s ease;
    font-weight: 500;
}

.nav-item:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
}

.nav-item.active {
    background-color: rgba(255, 255, 255, 0.2);
    color: white;
}

.navbar-right {
    display: flex;
    align-items: center;
}

.user-info {
    display: flex;
    align-items: center;
    color: white;
    cursor: pointer;
    padding: 8px 12px;
    border-radius: 4px;
    transition: background-color 0.3s ease;
}

.user-info:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.user-name {
    margin-right: 10px;
    font-weight: 500;
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-right: 8px;
}

.dropdown-arrow {
    font-size: 12px;
    opacity: 0.8;
}

/* 主体容器 */
.main-container {
    display: flex;
    margin-top: 60px;
    min-height: calc(100vh - 60px);
}

/* 左侧菜单栏 */
.sidebar {
    width: 240px;
    background-color: #2c3e50;
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
    transition: width 0.3s ease;
    position: fixed;
    left: 0;
    top: 60px;
    bottom: 0;
    overflow-y: auto;
    z-index: 999;
}

.sidebar.collapsed {
    width: 60px;
}

.sidebar-header {
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding: 0 15px;
    border-bottom: 1px solid #34495e;
}

.sidebar-toggle {
    background: none;
    border: none;
    color: #bdc3c7;
    font-size: 18px;
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.sidebar-toggle:hover {
    background-color: #34495e;
    color: white;
}

.sidebar-nav {
    padding: 10px 0;
}

.nav-menu {
    list-style: none;
}

.nav-item {
    margin-bottom: 2px;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    color: #bdc3c7;
    text-decoration: none;
    transition: all 0.3s ease;
    position: relative;
}

.nav-link:hover {
    background-color: #34495e;
    color: #3498db;
}

.nav-link.active {
    background-color: #3498db;
    color: white;
}

.nav-icon {
    width: 20px;
    text-align: center;
    margin-right: 12px;
    font-size: 16px;
}

.nav-text {
    flex: 1;
    font-weight: 500;
}

.submenu-arrow {
    font-size: 12px;
    transition: transform 0.3s ease;
}

.has-submenu.expanded .submenu-arrow {
    transform: rotate(90deg);
}

.submenu {
    list-style: none;
    background-color: #1a252f;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
}

.has-submenu.expanded .submenu {
    max-height: 200px;
}

.submenu-item {
    margin: 0;
}

.submenu-link {
    display: block;
    padding: 10px 20px 10px 52px;
    color: #95a5a6;
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 13px;
}

.submenu-link:hover {
    background-color: #34495e;
    color: #3498db;
}

.submenu-link.active {
    background-color: #3498db;
    color: white;
}

/* 主内容区域 */
.main-content {
    flex: 1;
    margin-left: 240px;
    padding: 20px;
    transition: margin-left 0.3s ease;
    background-color: #f5f7fa;
    min-height: calc(100vh - 60px);
}

.sidebar.collapsed + .main-content {
    margin-left: 60px;
}

/* 面包屑导航 */
.breadcrumb {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    font-size: 13px;
    color: #666;
}

.breadcrumb-item a {
    color: #3498db;
    text-decoration: none;
}

.breadcrumb-item a:hover {
    text-decoration: underline;
}

.breadcrumb-item.current {
    color: #333;
    font-weight: 500;
}

.breadcrumb-separator {
    margin: 0 8px;
    color: #ccc;
}

/* 页面标题区域 */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 24px;
    padding: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.page-title-section {
    flex: 1;
}

.page-title {
    font-size: 24px;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.page-subtitle {
    color: #7f8c8d;
    font-size: 14px;
    line-height: 1.6;
}

.page-actions {
    display: flex;
    gap: 12px;
    align-items: center;
}

/* 按钮样式 */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    white-space: nowrap;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 13px;
}

.btn-primary {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    color: white;
    box-shadow: 0 2px 4px rgba(52, 152, 219, 0.3);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #2980b9 0%, #21618c 100%);
    box-shadow: 0 4px 8px rgba(52, 152, 219, 0.4);
    transform: translateY(-1px);
}

.btn-secondary {
    background: white;
    color: #666;
    border: 1px solid #ddd;
}

.btn-secondary:hover {
    background: #f8f9fa;
    border-color: #3498db;
    color: #3498db;
}

.btn-icon {
    font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .sidebar {
        width: 60px;
    }
    
    .main-content {
        margin-left: 60px;
    }
    
    .navbar-center {
        display: none;
    }
    
    .page-header {
        flex-direction: column;
        gap: 16px;
    }
    
    .page-actions {
        width: 100%;
        justify-content: flex-end;
    }
}

/* 滚动条样式 */
.sidebar::-webkit-scrollbar {
    width: 6px;
}

.sidebar::-webkit-scrollbar-track {
    background: #2c3e50;
}

.sidebar::-webkit-scrollbar-thumb {
    background: #34495e;
    border-radius: 3px;
}

.sidebar::-webkit-scrollbar-thumb:hover {
    background: #4a6741;
}

/* 加载覆盖层 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-spinner {
    text-align: center;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 16px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    color: #666;
    font-size: 14px;
}

/* 消息提示 */
.message-toast {
    position: fixed;
    top: 80px;
    right: 20px;
    padding: 12px 20px;
    border-radius: 6px;
    color: white;
    font-size: 14px;
    font-weight: 500;
    z-index: 10000;
    animation: slideInRight 0.3s ease, fadeOut 0.3s ease 2.7s;
    animation-fill-mode: both;
}

.message-info {
    background: linear-gradient(135deg, #3498db, #2980b9);
}

.message-success {
    background: linear-gradient(135deg, #27ae60, #229954);
}

.message-error {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
}

.message-warning {
    background: linear-gradient(135deg, #f39c12, #e67e22);
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes fadeOut {
    from {
        opacity: 1;
    }
    to {
        opacity: 0;
        transform: translateX(100%);
    }
}
