<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工作回顾模块</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: "Microsoft YaHei", sans-serif;
        }
        body {
            background-color: #f5f5f5;
            color: #333;
            font-size: 14px;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        .page-header {
            background-color: #fff;
            border-radius: 4px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
            padding: 20px;
        }
        .page-title {
            font-size: 20px;
            font-weight: bold;
            color: #303133;
            margin-bottom: 10px;
        }
        .page-subtitle {
            color: #909399;
            font-size: 14px;
        }
        .filter-card {
            background-color: #fff;
            border-radius: 4px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
            overflow: hidden;
        }
        .filter-header {
            background-color: #f9f9f9;
            padding: 15px 20px;
            border-bottom: 1px solid #eee;
            font-weight: bold;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .filter-body {
            padding: 20px;
        }
        .filter-row {
            display: flex;
            flex-wrap: wrap;
            margin: 0 -10px 15px;
            align-items: center;
        }
        .filter-col {
            padding: 0 10px;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
        }
        .filter-label {
            margin-right: 10px;
            font-weight: normal;
            white-space: nowrap;
            min-width: 80px;
        }
        .form-select {
            width: 150px;
            height: 36px;
            padding: 0 10px;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            color: #606266;
            background-color: #fff;
            appearance: none;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 12 12'%3E%3Cpath fill='%23606266' d='M6 8.5L1.5 4 2.5 3l3.5 3.5L9.5 3l1 1z'/%3E%3C/svg%3E");
            background-repeat: no-repeat;
            background-position: right 10px center;
            padding-right: 30px;
        }
        .checkbox-wrapper {
            display: flex;
            align-items: center;
            margin-left: 20px;
        }
        .checkbox-wrapper input[type="checkbox"] {
            margin-right: 5px;
        }
        .btn {
            display: inline-block;
            padding: 8px 20px;
            border-radius: 4px;
            font-size: 14px;
            line-height: 1;
            text-align: center;
            cursor: pointer;
            border: 1px solid transparent;
            transition: all 0.3s;
            margin-left: 10px;
        }
        .btn-primary {
            background-color: #6366f1;
            color: #fff;
        }
        .btn-primary:hover {
            background-color: #5253cc;
        }
        .btn-default {
            background-color: #fff;
            border-color: #dcdfe6;
            color: #606266;
        }
        .btn-default:hover {
            border-color: #c6e2ff;
            color: #409eff;
        }
        .statistics-card {
            background-color: #fff;
            border-radius: 4px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
            overflow: hidden;
        }
        .statistics-header {
            background-color: #f9f9f9;
            padding: 15px 20px;
            border-bottom: 1px solid #eee;
            font-weight: bold;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .statistics-body {
            padding: 0;
        }
        .data-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 13px;
        }
        .data-table th,
        .data-table td {
            border: 1px solid #ebeef5;
            padding: 12px 8px;
            text-align: center;
            vertical-align: middle;
        }
        .data-table th {
            background-color: #f5f7fa;
            color: #606266;
            font-weight: 500;
            position: sticky;
            top: 0;
            z-index: 10;
        }
        .data-table tbody tr:hover {
            background-color: #f5f7fa;
        }
        .trend-up {
            color: #67c23a;
        }
        .trend-down {
            color: #f56c6c;
        }
        .trend-equal {
            color: #909399;
        }
        .number-cell {
            font-weight: 500;
        }
        .change-cell {
            font-weight: 500;
        }
        .percentage-cell {
            font-size: 12px;
        }
        .table-container {
            max-height: 600px;
            overflow-y: auto;
            border: 1px solid #ebeef5;
            border-radius: 4px;
        }
        .summary-stats {
            display: flex;
            justify-content: space-around;
            padding: 20px;
            background-color: #f8f9fa;
            margin-bottom: 20px;
        }
        .stat-item {
            text-align: center;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #6366f1;
        }
        .stat-label {
            color: #909399;
            font-size: 12px;
            margin-top: 5px;
        }
        .tab-container {
            margin-bottom: 20px;
        }
        .tab-nav {
            display: flex;
            background-color: #fff;
            border-radius: 4px 4px 0 0;
            border: 1px solid #ebeef5;
            border-bottom: none;
        }
        .tab-item {
            padding: 12px 20px;
            cursor: pointer;
            border-right: 1px solid #ebeef5;
            transition: all 0.3s;
        }
        .tab-item:last-child {
            border-right: none;
        }
        .tab-item.active {
            background-color: #6366f1;
            color: #fff;
        }
        .tab-item:hover:not(.active) {
            background-color: #f5f7fa;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
        .loading {
            text-align: center;
            padding: 40px;
            color: #909399;
        }
        .no-data {
            text-align: center;
            padding: 40px;
            color: #909399;
        }
        .export-btn {
            background-color: #67c23a;
            color: #fff;
        }
        .export-btn:hover {
            background-color: #5daf34;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面标题 -->
        <div class="page-header">
            <div class="page-title">📊 工作回顾模块</div>
            <div class="page-subtitle">基于日报数据的团队工作量统计分析，支持多维度对比和趋势分析</div>
        </div>

        <!-- 筛选条件 -->
        <div class="filter-card">
            <div class="filter-header">
                <span>📋 筛选条件</span>
                <button class="btn btn-primary" onclick="loadData()">查询</button>
            </div>
            <div class="filter-body">
                <div class="filter-row">
                    <div class="filter-col">
                        <label class="filter-label">时间范围:</label>
                        <select class="form-select" id="timeRange">
                            <option value="thisWeek">本周</option>
                            <option value="thisMonth" selected>本月</option>
                            <option value="thisQuarter">本季度</option>
                            <option value="thisYear">本年</option>
                        </select>
                    </div>
                    <div class="filter-col">
                        <label class="filter-label">部门:</label>
                        <select class="form-select" id="department">
                            <option value="">全部</option>
                            <option value="sales1">销售一部</option>
                            <option value="sales2">销售二部</option>
                            <option value="sales3">销售三部</option>
                            <option value="overseas">海外部</option>
                        </select>
                    </div>
                    <div class="filter-col">
                        <label class="filter-label">人员选择:</label>
                        <select class="form-select" id="personnel">
                            <option value="">全部</option>
                            <option value="user1">张三</option>
                            <option value="user2">李四</option>
                            <option value="user3">王五</option>
                        </select>
                    </div>
                    <div class="filter-col">
                        <div class="checkbox-wrapper">
                            <input type="checkbox" id="enableComparison" checked>
                            <label for="enableComparison">启用对比</label>
                        </div>
                    </div>
                    <div class="filter-col">
                        <div class="checkbox-wrapper">
                            <input type="checkbox" id="onlyDirect">
                            <label for="onlyDirect">仅直属</label>
                        </div>
                    </div>
                    <div class="filter-col">
                        <button class="btn btn-default export-btn" onclick="exportData()">导出数据</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 选项卡导航 -->
        <div class="tab-container">
            <div class="tab-nav">
                <div class="tab-item active" onclick="switchTab('summary')">汇总统计</div>
                <div class="tab-item" onclick="switchTab('personnel')">人员详细</div>
                <div class="tab-item" onclick="switchTab('department')">部门统计</div>
            </div>
        </div>
