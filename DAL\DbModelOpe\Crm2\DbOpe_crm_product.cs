using CRM2_API.BLL;
using CRM2_API.BLL.ServiceOpening;
using CRM2_API.BLL.WorkLog;
using CRM2_API.Common.JWT;
using CRM2_API.DAL.DbCommon;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.BusinessModel;
using CRM2_API.Model.ControllersViewModel;
using CRM2_API.Model.ControllersViewModel.Common;
using CRM2_API.Model.System;
using DocumentFormat.OpenXml.Drawing;
using JiebaNet.Segmenter.Common;
using Mapster;
using NJsonSchema;
using NPOI.SS.Formula.Functions;
using NPOI.SS.Formula.UDF;
using SqlSugar;
using System;
using System.Linq.Expressions;
using static CRM2_API.Model.ControllersViewModel.VM_Contract;

namespace CRM2_API.DAL.DbModelOpe.Crm2
{
    /// <summary>
    /// crm_product表操作
    /// </summary>
    public class DbOpe_crm_product : DbOperateCrm2<Db_crm_product, DbOpe_crm_product>
    {
        /// <summary>
        /// 根据查询条件获取产品列表。
        /// 产品编码、产品名称：支持模糊搜索；
        /// 产品类型：可选：GTIS正式、DB邓白氏、VIP零售、环球搜、慧思学院、期刊、其他数据、组织产品；
        /// 产品状态：可选：启用、停用、草稿；
        /// 创建人：自动获取列表创建人列表；
        /// 创建时间：时间段搜索，最少时间单位日，时间格式：YYYY-MM-DD；
        /// 根据用户数据权限返回相关数据。
        /// 返回结果按创建时间降序排列。
        /// </summary>
        /// <param name="searchProductListIn"></param>
        /// <param name="total"></param>
        /// <returns></returns>
        public List<SearchProductList_Out> SearchProductList(SearchProductList_In searchProductListIn, ref int total)
        {



            return Queryable.Where(e => e.Deleted == false)
                .LeftJoin<Db_v_userwithorg>((e, u) => e.CreateUser == u.Id)
                //.WhereIF(!string.IsNullOrEmpty(searchProductListIn.ProductNum), e => e.ProductNum.Contains(searchProductListIn.ProductNum))
                .WhereIF(!string.IsNullOrEmpty(searchProductListIn.ProductNum), e => e.ProductNum == searchProductListIn.ProductNum)
                .WhereIF(!string.IsNullOrEmpty(searchProductListIn.ProductName), e => e.ProductName.Contains(searchProductListIn.ProductName))
                .WhereIF(searchProductListIn.ProductType != null && searchProductListIn.ProductType != 0, e => e.ProductType == searchProductListIn.ProductType)
                .WhereIF(searchProductListIn.ProductState != null, e => e.ProductState == searchProductListIn.ProductState)
                .WhereIF(searchProductListIn.CreateUser != null, e => searchProductListIn.CreateUser.Contains(e.CreateUser))
                .WhereIF(searchProductListIn.CreateDateStart != null, e => e.CreateDate >= ((DateTime)searchProductListIn.CreateDateStart).GetDaysStart())
                .WhereIF(searchProductListIn.CreateDateEnd != null, e => e.CreateDate <= ((DateTime)searchProductListIn.CreateDateEnd).GetDaysEnd())
                //.OrderByDescending(e => e.CreateDate)
                .OrderBy(e => e.ProductNum)
                .Select<SearchProductList_Out>((e, u) => new SearchProductList_Out { CreateUser = u.Name, CreateDate = e.CreateDate.ToString("yyyy-MM-dd HH:mm") }, true)
                .Mapper(it =>
                {
                    it.Price = Db.Queryable<Db_crm_product_price>()
                    .Where(e => e.Deleted == false)
                    .Where(e => e.ProductId.Equals(it.Id))
                    .OrderBy(e => new { e.ServiceCycle, e.Currency })
                    .Select(e => new ProductPrice
                    {
                        Id = e.Id,
                        ProductId = e.ProductId,
                        ProductName = it.ProductName,
                        Currency = e.Currency,
                        Price = e.Price,
                        ChargingParameters = e.ChargingParameters,
                    }, true)
                    .ToList();
                    //it.CreateUserName = Db.Queryable<Db_sys_user>()
                    //.Single(e => e.Id == it.CreateUser) == null ? "" : Db.Queryable<Db_sys_user>()
                    //.Single(e => e.Id == it.CreateUser).Name;
                })
                .ToPageList(searchProductListIn.PageNumber, searchProductListIn.PageSize, ref total);
        }

        /// <summary>
        /// 根据id获取产品
        /// </summary>
        /// <param name="Ids"></param>
        /// <returns></returns>
        public GetProductById_Out GetProductById(string Ids)
        {
            return Queryable.Where(e => e.Deleted == false)
                .Where(e => e.Id.Equals(Ids))
                .Select<GetProductById_Out>(e => new GetProductById_Out { CreateDate = e.CreateDate.ToString("yyyy-MM-dd HH:mm") }, true)
                //.Mapper(e => e.ProductPrice, e => e.ProductPrice.First().ProductId)
                .Mapper(it =>
                {
                    it.ProductPrice = Db.Queryable<Db_crm_product_price>()
                    .Where(e => e.Deleted == false)
                    .Where(e => e.ProductId.Equals(Ids))
                    .OrderBy(e => new { e.Currency, e.ServiceCycle })
                    .Select(e => new ProductPriceById
                    {
                        Id = e.Id,
                        ProductId = e.ProductId,
                        ProductName = it.ProductName,
                        PriceMode = e.PriceMode,
                        SalesFloor = e.SalesFloor,
                        PricePart2 = e.PricePart2,
                        Currency = e.Currency,
                        Price = e.Price,
                        ChargingParameters = e.ChargingParameters,
                        PriceInfo = e.PriceInfo.Value,
                    }, true)
                    .Mapper(hit =>
                    {
                        switch (hit.ServiceCycle)
                        {
                            case 1:
                                hit.ServiceCycleStart = 12;
                                hit.ServiceCycleEnd = 14;
                                break;
                            case 2:
                                hit.ServiceCycleStart = 24;
                                hit.ServiceCycleEnd = 30;
                                break;
                            case 3:
                                hit.ServiceCycleStart = 36;
                                hit.ServiceCycleEnd = 48;
                                break;
                            default:
                                hit.ServiceCycleStart = hit.ServiceCycleStart * 12;
                                hit.ServiceCycleEnd = hit.ServiceCycleEnd * 13;
                                break;
                        }


                        hit.ProductCombination = Db.Queryable<Db_crm_product_combination>()
                        .InnerJoin<Db_crm_product_price>((e, f) => e.SubProductId.Equals(f.ProductId) && f.Currency == hit.Currency && f.ServiceCycle == hit.ServiceCycle)
                        .InnerJoin<Db_crm_product>((e, f, g) => e.SubProductId.Equals(g.Id))
                        .Where(e => e.Deleted == false)
                        .Where(e => e.ProductId.Equals(Ids))
                        .OrderBy(e => e.CreateDate)
                        .Select((e, f, g) => new ProductCombination
                        {
                            Id = e.Id,
                            ProductId = e.ProductId,
                            SubProductId = e.SubProductId,
                            ProductName = g.ProductName,
                            ProductType = g.ProductType,
                            //ServiceCycle = g.ServiceCycle == null ? 0 : g.ServiceCycle,
                            //ServiceCycleStart = g.ServiceCycleStart,
                            //ServiceCycleEnd = g.ServiceCycleEnd,
                            Currency = f.Currency,
                            Price = f.Price,
                            ChargingParameters = f.ChargingParameters,
                        }, true)
                        .Mapper(pc =>
                        {
                            pc.ServiceCycle = hit.ServiceCycle;
                            switch (pc.ServiceCycle)
                            {
                                case 1:
                                    pc.ServiceCycleStart = 12;
                                    pc.ServiceCycleEnd = 14;
                                    break;
                                case 2:
                                    pc.ServiceCycleStart = 24;
                                    pc.ServiceCycleEnd = 30;
                                    break;
                                case 3:
                                    pc.ServiceCycleStart = 36;
                                    pc.ServiceCycleEnd = 48;
                                    break;
                                default:
                                    pc.ServiceCycleStart = pc.ServiceCycleStart * 12;
                                    pc.ServiceCycleEnd = pc.ServiceCycleEnd * 13;
                                    break;
                            }
                        })

                        .ToList();
                    })
                    .ToList();

                    it.ProductCombination = Db.Queryable<Db_crm_product_combination>()
                    .InnerJoin<Db_crm_product>((e, f) => e.SubProductId.Equals(f.Id))
                    .Where(e => e.Deleted == false)
                    .Where(e => e.ProductId.Equals(Ids))
                    .OrderBy(e => e.CreateDate)
                    .Select((e, f) => new ProductCombination
                    {
                        Id = e.Id,
                        ProductId = e.ProductId,
                        SubProductId = e.SubProductId,
                        ProductName = f.ProductName,
                        ProductType = f.ProductType,
                        ServiceCycle = f.ServiceCycle,
                        ServiceCycleStart = f.ServiceCycleStart,
                        ServiceCycleEnd = f.ServiceCycleEnd,
                        ////ServiceCycleName = "",//g.ServiceCycle.ToEnum<>
                        //Currency = f.Currency,
                        //Price = f.Price,
                        //ChargingParameters = f.ChargingParameters,
                    }, true).Mapper(hit =>
                    {
                        hit.ProductName = hit.ProductType == (int)EnumProductType.SalesWits ? "慧销云进阶版" : hit.ProductName;
                        hit.ProductPrice = Db.Queryable<Db_crm_product_price>()
                        .Where(e => e.Deleted == false)
                        .Where(e => e.ProductId.Equals(hit.SubProductId))
                        .OrderBy(e => new { e.Currency, e.ServiceCycle })
                        .Select(e => new ProductPrice
                        {
                            Id = e.Id,
                            ProductId = e.ProductId,
                            ProductName = hit.ProductName,
                            ServiceCycle = e.ServiceCycle == null ? 0 : e.ServiceCycle,
                            Currency = e.Currency,
                            Price = e.Price,
                            ChargingParameters = e.ChargingParameters,
                        }).ToList();
                    }).ToList();

                    it.CreateUserName = Db.Queryable<Db_sys_user>()
                    .Single(e => e.Id == it.CreateUser) == null ? "" : Db.Queryable<Db_sys_user>()
                    .Single(e => e.Id == it.CreateUser).Name;
                })
                .First();
        }

        /// <summary>
        /// 根据查询条件获取产品信息，状态为启用的产品信息。
        /// </summary>
        /// <param name="getProductListIn"></param>
        /// <param name="total"></param>
        /// <returns></returns>
        public List<GetProductList_Out> GetProductList(GetProductList_In getProductListIn, ref int total)
        {
            bool isAllow = true;
            //修改 2024年3月11日 增加角色验证，只在角色是普通销售的人员进行权限验证
            //普通销售角色ID： 9fbe8362-b96f-46da-902a-aae767aa8b94
            //销售主管角色ID：313f9d6b-8feb-462c-9bdb-703436a37026
            string UserId = TokenModel.Instance.id;
            var IsSaler = DbOpe_sys_userinrole.Instance.GetData(r => r.UserId == UserId);
            //是当前是普通销售角色，做验证
            if (IsSaler != null && (IsSaler.RoleId == "9fbe8362-b96f-46da-902a-aae767aa8b94" || IsSaler.RoleId == "313f9d6b-8feb-462c-9bdb-703436a37026"))
            {
                var GetRuleData = DbOpe_crm_product_rules_special.Instance.GetData(r => r.UserId == UserId);
                if (GetRuleData.IsNotNull())
                {
                    isAllow = GetRuleData.AllowGtisExport;
                }
                else
                {
                    isAllow = false;
                }
            }
            //检查当前近甲方公司的所有合同中是否含有已经开通过的gtis或vip产品
            int oldGtis = -1;
            if (getProductListIn.FirstParty.IsNotNullOrEmpty())
            {
                var firstPartyGtisProduct = DbOpe_crm_contract.Instance.CheckFirstPartyHaveGtisOrVip(getProductListIn.FirstParty.ToString());
                if (firstPartyGtisProduct.IsNotNull() && firstPartyGtisProduct.Count > 0)
                {
                    oldGtis = firstPartyGtisProduct.FirstOrDefault().ProductType.Value;
                }
            }


            List<int> AddSouseType = new List<int> { (int)EnumProductType.SalesWits, (int)EnumProductType.AddCredit, (int)EnumProductType.AdditionalResource };

            SalesWitsReleaseMonths checkCanAddSalesWits = new SalesWitsReleaseMonths();
            if (getProductListIn.ParentContractId.IsNotNullOrEmpty())
            {
                var parentContractProudctList = DbOpe_crm_contract_productinfo.Instance.GetIncreaseContractProduct(getProductListIn.ParentContractId, getProductListIn.ContractId);
                if (!parentContractProudctList.Where(p => p.ProductType == EnumProductType.Gtis).Any() && !parentContractProudctList.Where(p => p.ProductType == EnumProductType.SalesWits).Any())
                {
                    AddSouseType = new List<int>();
                }
                else if (parentContractProudctList.Where(p => p.ProductType == EnumProductType.SalesWits).Any())
                {
                    AddSouseType.Remove((int)EnumProductType.SalesWits);
                }
                if (AddSouseType.Count > 0)
                {
                    checkCanAddSalesWits = GetAddItemContractGtisReleaseMonths(getProductListIn.ParentContractId, true);
                    if (checkCanAddSalesWits.ReleaseYears < 1)
                    {
                        AddSouseType = new List<int>();
                    }
                }

            }

            return Queryable.Where(e => e.Deleted == false)
                .Where(e => e.ProductState == 1)
                .WhereIF(!string.IsNullOrEmpty(getProductListIn.ProductName), e => e.ProductName.Contains(getProductListIn.ProductName))
                .WhereIF(getProductListIn.ProductType != null && getProductListIn.ProductType != 0, e => e.ProductType == getProductListIn.ProductType)
                .WhereIF(getProductListIn.SalesFloor != null && getProductListIn.SalesFloor != 0, e => e.NotForSale == 0)
                .WhereIF(!isAllow, e => e.Id != "716c3f51-afd0-4087-8cd3-9f9db8225cad")//专业版出口ID
                .WhereIF(getProductListIn.ContractType.IsNotNull() && getProductListIn.ContractType == (int)EnumContractType.AddItem, e => AddSouseType.Contains(e.ProductType.Value))
                .WhereIF(getProductListIn.ParentContractId.IsNotNullOrEmpty(), e => AddSouseType.Contains(e.ProductType.Value))
                .OrderBy(e => e.ProductNum)
                .Select<GetProductList_Out>(e => new GetProductList_Out
                {
                    StandardPriceCNY = e.CNYTypical,
                    StandardPriceUSD = e.USDTypical,
                    StandardPriceEUR = e.EURTypical,
                    SubCount = SqlFunc.IIF(SqlFunc.IsNullOrEmpty(e.SubCount), 0, SqlFunc.IIF(getProductListIn.SalesFloor == 1, e.SuperSubCount, e.SubCount))
                }, true)
                .Mapper(it =>
                {
                    if (it.ProductType == EnumProductType.Vip || it.ProductType == EnumProductType.Other || it.ProductType == EnumProductType.SalesWits || it.ProductType == EnumProductType.AddCredit || it.ProductType == EnumProductType.AdditionalResource || it.Id == "7f75e303-5e07-11f0-9097-c025a58cf040")
                    {
                        it.Disabled = 1;
                    }
                    if (getProductListIn.ParentContractId.IsNotNullOrEmpty())
                    {
                        if (it.ProductType == EnumProductType.SalesWits || it.ProductType == EnumProductType.AdditionalResource)
                        {
                            it.ServiceCycle = checkCanAddSalesWits.ReleaseYears;
                            it.ServiceCycleStart = checkCanAddSalesWits.RelesseMonths;
                            it.ServiceCycleEnd = checkCanAddSalesWits.ReleaseOpenMonths;
                        }
                    }
                    it.ProductCombination = Db.Queryable<Db_crm_product_combination>()
                    .InnerJoin<Db_crm_product>((e, f) => e.SubProductId.Equals(f.Id))
                    .Where(e => e.Deleted == false)
                    .Where(e => e.ProductId.Equals(it.Id))
                    .OrderBy(e => e.CreateDate)
                    .Select((e, f) => new ProductCombination
                    {
                        Id = e.Id,
                        ProductId = e.ProductId,
                        SubProductId = e.SubProductId,
                        ProductName = f.ProductName,
                        ProductType = f.ProductType,
                        ServiceCycle = f.ServiceCycle,
                        ServiceCycleStart = f.ServiceCycleStart,
                        ServiceCycleEnd = f.ServiceCycleEnd,
                        ////ServiceCycleName = "",//g.ServiceCycle.ToEnum<>
                        //Currency = f.Currency,
                        //Price = f.Price,
                        //ChargingParameters = f.ChargingParameters,
                    }).Mapper(hit =>
                    {
                        bool NeedUseSalesFloor = false;
                        if (getProductListIn.SalesFloor == 1)
                        {
                            int FloorPriceCount = Db.Queryable<Db_crm_product_price>()
                                                    .Where(e => e.Deleted == false)
                                                    .Where(e => e.ProductId.Equals(hit.SubProductId))
                                                    .Where(e => e.SalesFloor == 1).Count();
                            if (FloorPriceCount > 0)
                            {
                                NeedUseSalesFloor = true;
                            }
                        }
                        hit.ProductName = hit.ProductType == (int)EnumProductType.SalesWits ? "慧销云进阶版" : hit.ProductName;

                        hit.ProductPrice = Db.Queryable<Db_crm_product_price>()
                        .Where(e => e.Deleted == false)
                        .Where(e => e.ProductId.Equals(hit.SubProductId))
                        .WhereIF(NeedUseSalesFloor, e => e.SalesFloor == 1)
                        .OrderBy(e => new { e.ServiceCycle, e.Currency })
                        .Select(e => new ProductPrice
                        {
                            Id = e.Id,
                            ProductId = e.ProductId,
                            ProductName = hit.ProductName,
                            Currency = e.Currency,
                            Price = e.Price,
                            ChargingParameters = e.ChargingParameters,
                        }, true).ToList();
                    }).ToList();

                    bool NeedUseSalesFloor = false;
                    if (getProductListIn.SalesFloor == 1)
                    {
                        int FloorPriceCount = Db.Queryable<Db_crm_product_price>()
                                                .Where(e => e.Deleted == false)
                                                .Where(e => e.ProductId.Equals(it.Id))
                                                .Where(e => e.SalesFloor == 1).Count();
                        if (FloorPriceCount > 0)
                        {
                            NeedUseSalesFloor = true;
                        }
                    }

                    it.ProductPrice = Db.Queryable<Db_crm_product_price>()
                    .Where(e => e.Deleted == false && e.ServiceCycle == 1)
                    .Where(e => e.ProductId.Equals(it.Id))
                    .WhereIF(NeedUseSalesFloor, e => e.SalesFloor == 1)
                    .WhereIF(getProductListIn.SalesFloor == 0, e => e.SalesFloor == getProductListIn.SalesFloor)
                    .OrderBy(e => new { e.ServiceCycle, e.Currency, e.Price, e.CreateDate })
                    .Select(e => new ProductPrice
                    {
                        Id = e.Id,
                        ProductId = e.ProductId,
                        ProductName = it.ProductName,
                        Currency = e.Currency,
                        SalesFloor = e.SalesFloor,
                        Price = e.Price,
                        PriceMode = e.PriceMode,
                        PricePart2 = (decimal)e.PricePart2,
                        ChargingParameters = e.ChargingParameters,
                    }, true)
                    .Mapper(itt =>
                    {
                        itt.EventMark = it.ProductType == EnumProductType.Event ? it.ReMark : null;
                        itt.ProductType = it.ProductType;
                        if (itt.ProductType == EnumProductType.Other && oldGtis != -1)
                        {
                            switch (oldGtis)
                            {
                                case (int)EnumProductType.Gtis:
                                    itt.Price = itt.Currency == 1 ? 3000 : 500;
                                    break;
                                case (int)EnumProductType.Vip:
                                    itt.Price = itt.Currency == 1 ? 6000 : 1000;
                                    break;
                                default: break;
                            }
                        }

                    })
                    .ToList();
                })
                .ToPageList(getProductListIn.PageNumber, getProductListIn.PageSize, ref total);
        }


        public SalesWitsReleaseMonths GetAddItemContractGtisReleaseMonths(string contractId, bool ShowProductList = false)
        {
            SalesWitsReleaseMonths result = new SalesWitsReleaseMonths();
            int releaseMonths = -1;
            var contractBase = Db.Queryable<Db_crm_contract_productinfo>()
                                 .LeftJoin<Db_crm_product>((c, p) => c.ProductId == p.Id).Where((c, p) => c.ContractId == contractId)
                                 .LeftJoin<Db_crm_contract>((c, p, cc) => c.ContractId == cc.Id)
                                 .Where((c, p) => p.ProductType == (int)EnumProductType.Gtis || p.ProductType == (int)EnumProductType.Vip || p.ProductType == (int)EnumProductType.SalesWits)
                                 .Select((c, p, cc) => new
                                 {
                                     FirstOpenMonth = c.FirstOpeningMonths,
                                     OpeningMonths = c.OpeningMonths,
                                     OpeningYears = c.OpeningYears,
                                     SignDate = cc.SigningDate,

                                 })
                                 .First();

            var haveService = Db.Queryable<Db_crm_contract_productserviceinfo_gtis_appl>().Where(gs => gs.ContractId == contractId && gs.State == (int)EnumProcessStatus.Pass).ToList();

            if (haveService.Count > 0)
            {
                var haveGTIS = Db.Queryable<Db_crm_contract_serviceinfo_gtis>().Where(gs => gs.ContractId == contractId && gs.State == (int)EnumContractServiceState.VALID).ToList();
                if (haveGTIS.Count > 0)
                {
                    if (Convert.ToDateTime(haveGTIS.FirstOrDefault().ServiceCycleStart) > DateTime.Now)
                    {
                        result.RelesseMonths = contractBase.OpeningMonths.Value;
                        result.ReleaseYears = contractBase.OpeningYears.Value;
                        result.ReleaseOpenMonths = contractBase.FirstOpenMonth.Value;
                        return result;
                    }

                    // var couponAndprivate = Db.Queryable<Db_crm_contract_productserviceinfo_gtis_appl>().Where(gs => gs.ContractId == contractId && gs.Id == haveGTIS.FirstOrDefault().ProductServiceInfoGtisApplId).First();
                    var couponAndprivate = haveService.Where(r => r.CouponIds != null).Select(r => r.CouponIds).JoinToString();
                    int couponMonth = couponAndprivate.IsNullOrEmpty() ? 0 : couponAndprivate.Split(",").Count();

                    int privateDay = haveService.Select(r => r.PerlongServiceDays).Sum().Value;

                    //合同产品中GTIS服务的开通月份 - （当前时间- 服务表中开始时间）废弃
                    //服务开始时间+服务开通月份 得到结束时间  与当前时间的差值 向上取月份
                    //int diffMonth = DateTime.Now.Subtract(Convert.ToDateTime(haveService.FirstOrDefault().ServiceCycleStart)).Days / 30;

                    DateTime theoryDate = Convert.ToDateTime(haveGTIS.FirstOrDefault().ServiceCycleStart).AddMonths(contractBase.OpeningMonths.Value).AddMonths(couponMonth).AddDays(privateDay);

                    //// 计算月份的原始差值（作为double类型）
                    //int months = (theoryDate.Year - DateTime.Now.Year) * 12 + (theoryDate.Month - DateTime.Now.Month);

                    //if (!ShowProductList)
                    //{
                    //    // 如果结束日期的日早于开始日期的日，则将月份差值加1
                    //    if (theoryDate.Day < DateTime.Now.Day)
                    //    {
                    //        months++;
                    //    }
                    //}

                    int months = CalculateServiceMonths(DateTime.Now, theoryDate);
                    result.RelesseMonths = months > 0 ? months : 0;
                    result.ReleaseOpenMonths = result.RelesseMonths;

                    List<ProductMonthYear_Out> MonthYear = BLL_Contract.Instance.GetProductMonthYear();
                    if (MonthYear.Where(m => m.Month == months).Any())
                    {
                        result.ReleaseYears = MonthYear.Where(m => m.Month == months).FirstOrDefault().Year.Value;
                    }
                    else
                    {
                        if (ShowProductList)
                        {
                            result.ReleaseYears = months / 12;
                        }
                        else
                        {
                            result.ReleaseYears = (int)Math.Ceiling((double)months / 12);
                        }
                    }
                    result.ReleaseYears = result.ReleaseYears > 3 ? 3 : result.ReleaseYears;
                }
                else
                {
                    if (haveService.Where(r => r.IsInvalid == 0).Any())
                    {
                        if (Convert.ToDateTime(haveService.Where(r => r.IsInvalid == 0).FirstOrDefault().ServiceCycleStart) > DateTime.Now)
                        {
                            result.RelesseMonths = contractBase.OpeningMonths.Value;
                            result.ReleaseYears = contractBase.OpeningYears.Value;
                            result.ReleaseOpenMonths = contractBase.FirstOpenMonth.Value;
                            return result;
                        }

                        // var couponAndprivate = Db.Queryable<Db_crm_contract_productserviceinfo_gtis_appl>().Where(gs => gs.ContractId == contractId && gs.Id == haveGTIS.FirstOrDefault().ProductServiceInfoGtisApplId).First();
                        var couponAndprivate = haveService.Where(r => r.CouponIds != null).Select(r => r.CouponIds).JoinToString();
                        int couponMonth = couponAndprivate.IsNullOrEmpty() ? 0 : couponAndprivate.Split(",").Count();

                        int privateDay = haveService.Select(r => r.PerlongServiceDays).Sum().Value;

                        //合同产品中GTIS服务的开通月份 - （当前时间- 服务表中开始时间）废弃
                        //服务开始时间+服务开通月份 得到结束时间  与当前时间的差值 向上取月份
                        //int diffMonth = DateTime.Now.Subtract(Convert.ToDateTime(haveService.FirstOrDefault().ServiceCycleStart)).Days / 30;

                        DateTime theoryDate = Convert.ToDateTime(haveService.Where(r => r.IsInvalid == 0).FirstOrDefault().ServiceCycleStart).AddMonths(contractBase.OpeningMonths.Value).AddMonths(couponMonth).AddDays(privateDay);

                        int months = CalculateServiceMonths(DateTime.Now, theoryDate);
                        result.RelesseMonths = months > 0 ? months : 0;
                        result.ReleaseOpenMonths = result.RelesseMonths;

                        List<ProductMonthYear_Out> MonthYear = BLL_Contract.Instance.GetProductMonthYear();
                        if (MonthYear.Where(m => m.Month == months).Any())
                        {
                            result.ReleaseYears = MonthYear.Where(m => m.Month == months).FirstOrDefault().Year.Value;
                        }
                        else
                        {
                            if (ShowProductList)
                            {
                                result.ReleaseYears = months / 12;
                            }
                            else
                            {
                                result.ReleaseYears = (int)Math.Ceiling((double)months / 12);
                            }
                        }
                        result.ReleaseYears = result.ReleaseYears > 3 ? 3 : result.ReleaseYears;
                    }
                    else
                    {
                        result.RelesseMonths = contractBase.OpeningMonths.Value;
                        result.ReleaseYears = contractBase.OpeningYears.Value;
                        result.ReleaseOpenMonths = contractBase.FirstOpenMonth.Value;
                    }
                }
            }
            else
            {
                result.RelesseMonths = contractBase.OpeningMonths.Value;
                result.ReleaseYears = contractBase.OpeningYears.Value;
                result.ReleaseOpenMonths = contractBase.FirstOpenMonth.Value;
            }
            return result;
        }

        /// <summary>
        ///  参照 BLL_ServiceOpening.Instance.CalculateServiceMonths
        ///  方法相同
        /// </summary>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <returns></returns>
        /// <exception cref="CRM2_API.Model.System.ApiException"></exception>
        public int CalculateServiceMonths(DateTime startDate, DateTime endDate)
        {
            try
            {
                // 验证日期参数的合理性
                if (endDate <= startDate)
                {
                    var errorMsg = $"服务结束时间({endDate:yyyy-MM-dd})必须晚于开始时间({startDate:yyyy-MM-dd})";
                    LogUtil.AddErrorLog(errorMsg);
                    throw new CRM2_API.Model.System.ApiException(errorMsg);
                }

                // 计算年份和月份的差异
                var yearDiff = endDate.Year - startDate.Year;
                var monthDiff = endDate.Month - startDate.Month;

                // 基础月数 = 年份差异 * 12 + 月份差异
                var totalMonths = yearDiff * 12 + monthDiff;

                // 处理跨月的天数逻辑（自然月计费的核心逻辑）
                if (endDate.Day >= startDate.Day)
                {
                    // 结束日期的天数 >= 开始日期的天数，说明跨越了完整的月份
                    // 例如：1月15日 到 2月15日 = 1个完整月
                    // 例如：1月15日 到 2月20日 = 1个完整月 + 部分天数
                    totalMonths += 1;
                }
                // 如果 endDate.Day < startDate.Day，说明结束日期在当前计费周期内
                // 例如：7月28日开始，次年6月8日结束，6月8日在第11个计费周期内
                // totalMonths 保持不变，不需要额外处理

                // 验证计算结果的合理性
                if (totalMonths <= 0)
                {
                    var errorMsg = $"计算的服务月数异常，月数: {totalMonths}，开始时间: {startDate:yyyy-MM-dd}，结束时间: {endDate:yyyy-MM-dd}";
                    LogUtil.AddErrorLog(errorMsg);
                    throw new CRM2_API.Model.System.ApiException(errorMsg);
                }

                LogUtil.AddLog($"服务月数计算完成: {totalMonths}个月，计算逻辑: 年差={yearDiff}, 月差={monthDiff}, 开始日={startDate.Day}, 结束日={endDate.Day}");

                return totalMonths;
            }
            catch (CRM2_API.Model.System.ApiException)
            {
                // 重新抛出业务异常，保持原有的错误信息和上下文
                throw;
            }
            catch (Exception ex)
            {
                var errorMsg = $"计算服务月数异常，开始时间: {startDate:yyyy-MM-dd}，结束时间: {endDate:yyyy-MM-dd}，异常信息: {ex.Message}";
                LogUtil.AddErrorLog($"{errorMsg}，详细异常: {ex}");
                throw new CRM2_API.Model.System.ApiException(errorMsg);
            }
        }


        /// <summary>
        /// 修改产品状态信息，变更产品状态为启用、停用，多条记录全部执行成功则返回成功，否则返回失败。
        /// </summary>
        /// <param name="Ids"></param>
        /// <param name="State"></param>
        /// <param name="Remark"></param>
        /// <returns></returns>
        public void UpdateProductState(List<string> Ids, int State, string Remark)
        {
            //try
            //{

            //启用和草稿状态应该不能操作启用
            var exp = Expressionable.Create<Db_crm_product>();
            exp.And(it => it.ProductState == 2);//拼接OR
            exp.OrIF(State == 1, it => it.ProductState == 1);//拼接OR
            exp.OrIF(State == 0, it => it.ProductState == 0);                                               //		    
            int IsStartOrDraft = Queryable.Where(e => Ids.Contains(e.Id)).Where(exp.ToExpression()).Count();
            if (IsStartOrDraft > 0 && State == 1)
            {
                throw new ApiException("启用和草稿状态的产品不能操作启用");
            }
            //产品管理中停用、草稿状态应该不能操作停用
            if (IsStartOrDraft > 0 && State == 0)
            {
                throw new ApiException("停用、草稿状态的产品不能操作停用");
            }
            Updateable
                //.SetColumns(e => e.Remark == Remark)
                .SetColumnsIF(State == 0, e => e.ProductState == 0)
                .SetColumnsIF(State == 1, e => e.ProductState == 1)
                .SetColumns(e => e.IsRecommend == 0)
                .Where(e => Ids.Contains(e.Id))
                .ExecuteCommand();
            Instance.SaveQueues();
            //}
            //catch (Exception ex)
            //{
            //    string ErrorMessage = ex.InnerException.Message;
            //    throw new ApiException(ErrorMessage);
            //}
        }

        /// <summary>
        /// 删除产品信息，当产品状态为停用、草稿时，可删除。验证数据权限。只做逻辑删除，修改Deleted
        /// </summary>
        /// <param name="Ids"></param>
        /// <returns></returns>
        public void DeleteProduct(List<string> Ids)
        {
            int Data = 0;
            try
            {
                Updateable.SetColumns(e => e.Id == e.Id)
                      .SetColumns(e => e.Deleted == true)
                      .SetColumns(e => e.IsRecommend == 0)
                      .Where(e => Ids.Contains(e.Id))
                      .Where(e => e.ProductState == 0 || e.ProductState == 2)
                      .ExecuteCommand();
            }
            catch (Exception ex)
            {
                string ErrorMessage = ex.InnerException.Message;
                throw new ApiException(ErrorMessage);
            }



        }
        /// <summary>
        /// 更新产品表信息，废止
        /// 需要完整的产品表结构，包括不为空字段，否则会报错
        /// </summary>
        /// <param name="updateProduct_In"></param>
        /// <returns></returns>
        public int UpdateProduct(Db_crm_product updateProduct_In)
        {
            int Data = Db.Updateable(updateProduct_In).ExecuteCommand();

            return Data == 1 ? 1 : 0;
        }

        /// <summary>
        /// 更新产品价格信息，废止
        /// 理由同产品表
        /// </summary>
        /// <param name="updateProductPrice_In"></param>
        /// <returns></returns>
        public int UpdateProductPrice(List<Db_crm_product_price> updateProductPrice_In)
        {
            int Data = Db.Updateable(updateProductPrice_In).ExecuteCommand();

            return Data == 1 ? 1 : 0;
        }

        /// <summary>
        /// 逻辑删除组合表信息
        /// </summary>
        /// <param name="ProductId_In"></param>
        /// <exception cref="ApiException"></exception>
        public void LogicDeleteProductCombination(string ProductId_In)
        {
            try
            {
                Db.Updateable<Db_crm_product_combination>()
                     .SetColumns(e => e.Deleted == true)
                     .Where(e => e.ProductId == ProductId_In)
                     .ExecuteCommand();
            }
            catch (Exception ex)
            {
                throw new ApiException(ex.InnerException.Message.ToString());
            }
        }

        /// <summary>
        /// 修改产品信息。保存草稿：修改产品信息，状态为草稿。新建：修改产品信息，状态为启用。
        /// 表名:Crm_Product 产品表。表名:Crm_Product_Price 产品价格表。表名:Crm_Product_Combination 产品组合表。
        /// （修改应不改变产品状态，所以应该只有一个提交按钮。）只可在产品状态为草稿、停用时修改产品信息。
        /// </summary>
        /// <param name="updataProduct"></param>
        /// <param name="priceList"></param>
        /// <param name="productId"></param>
        /// <param name="combinationList"></param>
        /// <returns></returns>
        /// <exception cref="ApiException"></exception>
        public void UpdateProductInTrans(Db_crm_product updataProduct, List<Db_crm_product_price> priceList, string productId, List<Db_crm_product_combination> combinationList)
        {
            //Db.Updateable(updataProduct).ExecuteCommand();
            //Db.Updateable(priceList).ExecuteCommand();

            Db.Updateable<Db_crm_product>()
                .SetColumns(e => e.ProductName == updataProduct.ProductName)
                .SetColumns(e => e.ProductType == updataProduct.ProductType)
                .SetColumns(e => e.ServiceCycle == updataProduct.ServiceCycle)
                .SetColumns(e => e.IsRecommend == updataProduct.IsRecommend)
                .SetColumns(e => e.ServiceCycleStart == updataProduct.ServiceCycleStart)
                .SetColumns(e => e.ServiceCycleEnd == updataProduct.ServiceCycleEnd)
                .SetColumns(e => e.Remark == updataProduct.Remark)
                .SetColumns(e => e.ProductState == updataProduct.ProductState)
                .SetColumns(e => e.UpdateUser == updataProduct.UpdateUser)
                .SetColumns(e => e.UpdateDate == updataProduct.UpdateDate)
                .Where(e => e.Id == productId)
                .ExecuteCommand();

            priceList.ForEach(price =>
            {
                Db.Updateable<Db_crm_product_price>()
                .SetColumns(e => e.Currency == price.Currency)
                .SetColumns(e => e.Price == price.Price)
                .SetColumns(e => e.ChargingParameters == price.ChargingParameters)
                .SetColumns(e => e.UpdateUser == price.UpdateUser)
                .SetColumns(e => e.UpdateDate == price.UpdateDate)
                .Where(e => e.Id == price.Id)
                .ExecuteCommand();
            });

            combinationList.ForEach(combination =>
            {
                Db.Updateable<Db_crm_product_combination>()
                     .SetColumns(e => e.SubProductId == combination.SubProductId)
                     .Where(e => e.Id == combination.Id)
                     .ExecuteCommand();

            });
            DbOpe_crm_product_combination.Instance.SaveQueues();
        }

        public void UpdateProductInTrans(UpdateProduct_In updateProductIn, string userId)
        {
            var isStart = Queryable.Where(e => e.Id == updateProductIn.Id).Select(e => e.ProductState).First();
            if (isStart == 1)
            {
                throw new ApiException("启用状态下不可修改产品");
            }

            //增加判断：价格不能为同一种货币
            var hasSameMoney = updateProductIn.ProductPrice.GroupBy(e => new { e.ServiceCycle, e.Currency }).Where(g => g.Count() > 1).Count();
            if (hasSameMoney > 0)
            {
                throw new ApiException("产品价格不能有同一周期同一币种的多种价格，请检查");
            }

            if (updateProductIn.SubmitState == EStatus.Start && updateProductIn.ProductType == (int)EnumProductType.Combination && updateProductIn.ProductCombination.Count() == 0)
            {
                throw new ApiException("组合产品需要选择子产品");
            }

            Db_crm_product UpdateProduct = updateProductIn.MappingTo<Db_crm_product>();
            UpdateProduct.ProductState = (int)updateProductIn.SubmitState;
            UpdateProduct.UpdateUser = userId;
            UpdateProduct.UpdateDate = DateTime.Now;
            //草稿问题考量
            UpdateProduct.ServiceCycleStart = updateProductIn.ServiceCycleStart != null ? (int)updateProductIn.ServiceCycleStart : 0;
            UpdateProduct.ServiceCycleEnd = updateProductIn.ServiceCycleEnd != null ? (int)updateProductIn.ServiceCycleEnd : 0;
            UpdateProduct.ServiceCycle = updateProductIn.ServiceCycle != null ? (int)updateProductIn.ServiceCycle : 1;

            Db.Updateable<Db_crm_product>()
                    .SetColumns(e => e.ProductName == UpdateProduct.ProductName)
                    .SetColumns(e => e.ProductNameEN == UpdateProduct.ProductNameEN)
                    .SetColumns(e => e.ProductDescription == UpdateProduct.ProductDescription)
                    .SetColumns(e => e.ProductDescriptionEn == UpdateProduct.ProductDescriptionEn)
                    .SetColumns(e => e.ProductType == UpdateProduct.ProductType)
                    .SetColumns(e => e.ServiceCycle == UpdateProduct.ServiceCycle)
                    .SetColumns(e => e.IsRecommend == UpdateProduct.IsRecommend)
                    .SetColumns(e => e.SuperSubCount == UpdateProduct.SuperSubCount)
                    .SetColumns(e => e.GtisReport == UpdateProduct.GtisReport)
                    .SetColumns(e => e.ServiceCycleStart == UpdateProduct.ServiceCycleStart)
                    .SetColumns(e => e.ServiceCycleEnd == UpdateProduct.ServiceCycleEnd)
                    .SetColumns(e => e.Remark == UpdateProduct.Remark)
                    .SetColumns(e => e.ProductState == UpdateProduct.ProductState)
                    .SetColumns(e => e.UpdateUser == UpdateProduct.UpdateUser)
                    .SetColumns(e => e.UpdateDate == UpdateProduct.UpdateDate)
                    .Where(e => e.Id == UpdateProduct.Id)
                    .AddQueue();

            //价格表更新
            List<Db_crm_product_price> PriceList = new List<Db_crm_product_price>();

            updateProductIn.ProductPrice.ForEach(item =>
            {
                Db_crm_product_price price = item.MappingTo<Db_crm_product_price>();

                if (price.Id.IsNotNullOrEmpty())
                {
                    int serviceCycle = price.ServiceCycle != null ? (int)price.ServiceCycle : 1;
                    decimal perPrice = price.Price / serviceCycle;

                    Db.Updateable<Db_crm_product_price>()
                            .SetColumns(e => e.Currency == price.Currency)
                            .SetColumns(e => e.Price == price.Price)
                            .SetColumns(e => e.ServiceCycle == price.ServiceCycle)
                            //.SetColumns(e => e.PerPrice == perPrice)
                            .SetColumns(e => e.SalesFloor == price.SalesFloor)
                            .SetColumns(e => e.PriceMode == price.PriceMode)
                            .SetColumns(e => e.PricePart2 == price.PricePart2)
                            .SetColumns(e => e.ChargingParameters == price.ChargingParameters)
                            .SetColumns(e => e.UpdateUser == userId)
                            .SetColumns(e => e.UpdateDate == DateTime.Now)
                            .Where(e => e.Id == price.Id)
                            .Where(e => e.Deleted == false)
                            //.Where(e => e.ProductId == price.ProductId)
                            .AddQueue();
                }
                else
                {
                    price.Id = Guid.NewGuid().ToString();
                    price.ProductId = UpdateProduct.Id;
                    //修改 2024年1月18日 服务周期放到价格中
                    price.ServiceCycle = item.ServiceCycle != null ? (int)item.ServiceCycle : 1;
                    //price.PerPrice = price.Price / price.ServiceCycle;

                    //2024年2月21日 增加
                    //是否区分国内外售卖 传入空则默认为0 不区分/国内价格 传入1则为国外价格
                    price.SalesFloor = item.SalesFloor != null ? (int)item.SalesFloor : 0;
                    //计价方式 详见 EnumPriceMode
                    price.PriceMode = item.PriceMode;
                    //区间上限/复购价/自费价
                    price.PricePart2 = item.PricePart2;


                    price.Deleted = false;
                    price.CreateUser = userId;
                    price.CreateDate = DateTime.Now;

                    price.UpdateUser = userId;
                    price.UpdateDate = DateTime.Now;
                    PriceList.Add(price);

                }
            });
            //价格表也应考虑删除 以及新增的问题
            DbOpe_crm_product_price.Instance.InsertQueue(PriceList);
            List<string> oldPriceIds = Db.Queryable<Db_crm_product_price>()
                        .Where(e => e.Deleted == false)
                        .Where(e => e.ProductId == UpdateProduct.Id)
                        .Select(e => e.Id)
                        .ToList();
            List<string> NeedDelPriceIds = oldPriceIds.Except(updateProductIn.ProductPrice.Select(e => e.Id)).ToList();
            Db.Updateable<Db_crm_product_price>().SetColumns(e => e.Deleted == true)
                        .Where(e => NeedDelPriceIds.Contains(e.Id))
                        .AddQueue();



            //组合表更新,不存在update,只存在delete与Insert
            List<Db_crm_product_combination> CombinationInsertList = new List<Db_crm_product_combination>();
            updateProductIn.ProductCombination.ForEach(item =>
            {
                Db_crm_product_combination Combination = item.MappingTo<Db_crm_product_combination>();

                if (!Combination.Id.IsNotNullOrEmpty())
                {
                    Combination.Id = Guid.NewGuid().ToString();
                    Combination.ProductId = UpdateProduct.Id;
                    Combination.SubProductId = item.SubProductId;
                    Combination.Deleted = false;
                    Combination.CreateUser = userId;
                    Combination.CreateDate = DateTime.Now;
                    Combination.UpdateUser = userId;
                    Combination.UpdateDate = DateTime.Now;
                    CombinationInsertList.Add(Combination);
                }
            });
            DbOpe_crm_product_combination.Instance.InsertQueue(CombinationInsertList);
            List<string> oldComIds = Db.Queryable<Db_crm_product_combination>()
                       .Where(e => e.Deleted == false)
                       .Where(e => e.ProductId == UpdateProduct.Id)
                       .Select(e => e.Id)
                       .ToList();
            List<string> NeedDelComIds = oldComIds.Except(updateProductIn.ProductCombination.Select(e => e.Id)).ToList();
            Db.Updateable<Db_crm_product_combination>().SetColumns(e => e.Deleted == true)
                        .Where(e => NeedDelComIds.Contains(e.Id))
                        .AddQueue();
            WorkLog_Product.Instance.AddProductWorkLog(updateProductIn.Id, EnumProductLogType.Modify, updateProductIn.Remark);
            //执行所有操作
            Instance.SaveQueues();
        }



        /// <summary>
        /// 根据产品类型，获取计费参数
        /// </summary>
        /// <param name="productType"></param>
        public GetProductChargingParameters_Out GetChargingParametersByProductType(int productType)
        {
            return Db.Queryable<Db_crm_producttype_charging>().Where(e => e.Deleted == false)
                .Where(e => e.ProductType == productType)
                .Select<GetProductChargingParameters_Out>().First();
        }

        /// <summary>
        /// 根据当前人员ID，返回需要查询的相关权限人的ID列表
        /// </summary>
        /// <param name="UserId"></param>
        /// <param name="isGetManger"></param>
        /// <returns></returns>
        public List<string> GetOrgUserByRoleUser(string UserId, bool isGetManger = false)
        {
            List<string> Ids = new List<string>();
            //1.1 若是需要获取管理者，则需要根据userid或者自身机构ID，并向上检查所有上级组织管理者
            if (isGetManger)
            {
                string orgId = Db.Queryable<Db_sys_user>().Where(e => e.Deleted == false)
                        .Where(e => e.Id == UserId)
                        .Select(e => e.OrganizationId).First();
                if (orgId == "********-0000-0000-0000-********0000")
                {
                    return Ids;
                }
                ////2023年11月15日 启用此参数，内部逻辑处理去除非销售组织的管理员，但保留其中销售主管角色下所有管理员
                ////2023年11月16日 修改为销售总监角色
                //var RoleManagerUser = Db.Queryable<Db_sys_user>().Where(e => e.Deleted == false)
                //                   .LeftJoin<Db_sys_userinrole>((e, f) => e.Id == f.UserId && f.Deleted == false)
                //                   .Where(e => e.Id != TokenModel.Instance.id)
                //                   .Where((e, f) => f.RoleId == "0cafa3b1-1d14-4047-ae52-2feb0bf2c1cc")
                //                   .Where((e, f) => e.OrganizationId == "********-0000-0000-0000-********0000")
                //                   .Select(e => e.Id).ToList();
                //Ids.AddRange(RoleManagerUser);
                var orgList = Db.Queryable<Db_sys_organization>().ToChildList(it => it.ParentId, orgId).Select(e => e.Id).ToList();
                var userList = Db.Queryable<Db_sys_user>().Where(e => e.Deleted == false)
                            .Where(e => orgList.Contains(e.OrganizationId))
                            .Where(e => e.OrganizationId != "********-0000-0000-0000-********0000")
                            .Where(e => e.UserType == 1)
                            .Where(e => e.UserStatus == true)
                            .OrderBy(e => e.UserType)
                            .Select(e => e.Id).ToList();
                Ids.AddRange(userList);
            }
            else
            {
                //2.根据当前获取所属组织及是否管理者，非管理者直接返回自己
                int isManager = 0;
                isManager = Db.Queryable<Db_sys_user>().Where(e => e.Id == UserId)
                    .Select(e => e.UserType).First();
                if (isManager == 2)
                {
                    //非管理者直接返回自己
                    Ids.Add(UserId);
                }
                else
                {
                    //3.根据管理者获取完整管理人员列表 
                    //3.1 管理者返回机构id
                    string orgId = Db.Queryable<Db_sys_user>().Where(e => e.Deleted == false)
                        .Where(e => e.Id == UserId)
                        .Select(e => e.OrganizationId).First();
                    //3.3 根据机构id查询其下所属的所有人员id
                    var orgList = Db.Queryable<Db_sys_organization>().ToChildList(it => it.ParentId, orgId).Select(e => e.Id).ToList();

                    var userList = Db.Queryable<Db_sys_user>().Where(e => e.Deleted == false)
                            .Where(e => orgList.Contains(e.OrganizationId))
                            .Where(e => e.UserStatus == true)
                            //.Where(e => e.OrganizationId != "********-0000-0000-0000-********0000")
                            .OrderBy(e => e.UserType)
                            .Select(e => e.Id).ToList();
                    Ids.AddRange(userList);

                }
            }
            return Ids;
        }



        public GetProductById_Out ObjectTest()
        {
            var list = Queryable.ToList();
            var result = list.Adapt<List<GetProductById_Out>>();
            return result.First();
        }

        /// <summary>
        /// 事务方式 添加产品，包含产品价格及组合内容
        /// </summary>
        /// <param name="crm_Product"></param>
        /// <param name="priceList"></param>
        /// <param name="combinationList"></param>
        /// <exception cref="ApiException"></exception>
        public void AddProductInTrans(Db_crm_product crm_Product, List<Db_crm_product_price> priceList, List<Db_crm_product_combination> combinationList)
        {

            Instance.InsertQueue(crm_Product);

            DbOpe_crm_product_price.Instance.InsertQueue(priceList);

            DbOpe_crm_product_combination.Instance.InsertQueue(combinationList);


            //取消注释 看看到底错在哪
            WorkLog_Product.Instance.AddProductWorkLog(crm_Product.Id, EnumProductLogType.Add, crm_Product.Remark);

            Instance.SaveQueues();

        }

        /// <summary>
        /// 获取产品列表用于下拉菜单，无筛选条件
        /// </summary>
        public List<GetProductNameListNoCondition> GetProductNameListNoCondition(bool haveCombination)
        {
            return Queryable.Where(e => e.Deleted == false)
                .Where(e => e.ProductState == 1)
                .WhereIF(haveCombination, e => e.ProductType != 2)
                .OrderBy(e => new { e.ProductType, e.CreateDate })
                .Select<GetProductNameListNoCondition>()
                .ToList();
        }


        public bool ProductNameValidating(string SearchName)
        {
            bool exist = false;
            exist = Queryable.Where(e => e.ProductName == SearchName).Where(e => e.Deleted == false).Select(e => e.Id).Count() == 0 ? false : true;
            return exist;
        }


        /// <summary>
        /// 根据产品Id列表获取启用状态的产品的服务信息列表(产品ID+服务周期)
        /// </summary>
        /// <param name="productIds"></param>
        /// <returns></returns>
        public List<GetUnlimitedContractTemplateList_Trans> GetProductServiceCycleById(List<string>? productIds)
        {
            return Queryable
                .WhereIF(productIds.Count != 0, e => productIds.Contains(e.Id))
                .Where(e => e.Deleted == false)
                .Where(e => e.ProductState == 1)
                .Select(e => new GetUnlimitedContractTemplateList_Trans
                {
                    ProductId = e.Id,
                    ServiceCycle = e.ServiceCycle == null ? 1 : (int)e.ServiceCycle,
                })
                .ToList();
        }


        /// <summary>
        /// 根据服务年限筛选产品列表
        /// </summary>
        /// <param name="cycle"></param>
        /// <returns></returns>
        public List<GetProductListByServiceCycle_Out> GetProductListByServiceCycle(int? cycle)
        {
            return Queryable
                 .WhereIF(cycle != null, e => e.ServiceCycle == cycle)
                 .Where(e => e.Deleted == false)
                 .Where(e => e.ProductState == 1)
                 .Select<GetProductListByServiceCycle_Out>()
                 .ToList();
        }

        /// <summary>
        /// 根据产品IdList获取服务年限，如果存在不同的服务年限，返回null
        /// </summary>
        /// <param name="search_In"></param>
        /// <returns></returns>
        public int? GetServiceCycleByProductList(GetServiceCycleByProductList_In search_In)
        {
            var list = Queryable
                .WhereIF(search_In.ProductIdList.Count > 0, e => search_In.ProductIdList.Contains(e.Id))
                .Where(e => e.Deleted == false)
                .Where(e => e.ProductState == 1)
                .GroupBy(e => e.ServiceCycle)
                .Select(e => e.ServiceCycle)
                .ToList();

            if (list.Count != 1)
                return null;
            else
                return list.First();
        }
        /// <summary>
        /// 返回推荐产品剩余数量，最大为3
        /// </summary>
        /// <returns></returns>
        public int GetIsRecommendCount()
        {
            int isRecommendCount = Queryable.Where(e => e.Deleted == false)
                                   .Where(e => e.IsRecommend == 1)
                                   .Count();
            int returnValue = isRecommendCount >= 3 ? 0 : 3 - isRecommendCount;
            return returnValue;

        }

        /// <summary>
        /// 根据产品id返回操作记录
        /// </summary>
        /// <param name="ProductId"></param>
        /// <returns></returns>
        public List<ProductOperateLog> GetProductOperateLog(string ProductId)
        {
            return Db.Queryable<Db_sys_operation_product_log>()
                     .Where(e => e.productId == ProductId)
                     .OrderByDescending(e => e.opterateTime)
                     .Select<ProductOperateLog>()
                     .Mapper(it =>
                     {
                         it.OpterateTime = Convert.ToDateTime(it.OpterateTime).ToString("yyyy-MM-dd HH:mm");
                         string UserAvatar = Db.Queryable<Db_sys_user>().Where(e => e.Id == it.UserId).Select(e => e.AvatarImage).First();
                         it.UserAvatar = UserAvatar == null ? "" : UserAvatar;
                     })
                     //.Select<ProductOperateLog>(e => new ProductOperateLog { OpterateTime = e.opterateTime.ToString("yyyy-MM-dd HH:mm") }, true)
                     .ToList();
        }

        /// <summary>
        /// 根据产品别名获取产品信息
        /// </summary>
        /// <param name="productName"></param>
        /// <returns></returns>
        public Db_crm_product? GetProductByContrinsTrimAliss(string productName)
        {
            var productN = new ChineseAnalyzerCls().ReplaceKeyword(productName);
            return Queryable
                .Where(e => productN.Contains(e.Aliss.Trim()))
                .Where(e => e.Deleted == false)
                .First();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="productIds"></param>
        /// <returns></returns>
        public List<GetTemplateProductList_Out> GetTemplateProductList(List<string> productIds)
        {
            return Queryable
                .Where(e => productIds.Contains(e.Id))
                .Where(e => e.Deleted == false)
                .Select<GetTemplateProductList_Out>()
                .ToList();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="productIds"></param>
        /// <returns></returns>
        public List<Db_crm_product> GetAllProductList(List<string> productIds)
        {
            return Queryable
                .Where(e => productIds.Contains(e.Id))
                .ToList();
        }

        public List<SearchSpecialProductRules_Out> SearchSpecialProductRules(SearchSpecialProductRules_In searchSpecialProductRules_In, ref int total)
        {
            return Db.Queryable<Db_v_userwithorg>()
                .LeftJoin<Db_crm_product_rules_special>((u, prs) => u.Id == prs.UserId)
                .WhereIF(searchSpecialProductRules_In.UserName.IsNotNullOrEmpty(), (u, prs) => u.Name.Contains(searchSpecialProductRules_In.UserName))
                .WhereIF(searchSpecialProductRules_In.UserOrgId.IsNotNullOrEmpty(), (u, prs) => u.OrganizationId == searchSpecialProductRules_In.UserOrgId)
                .WhereIF(searchSpecialProductRules_In.SpecialProductRules == EnumSpecialProductRules.IsAllowSuperSubAccount, (u, prs) => prs.AllowSuperSubAccount == true)
                .WhereIF(searchSpecialProductRules_In.SpecialProductRules == EnumSpecialProductRules.IsAllowGtisImportVIP, (u, prs) => prs.AllowGtisImportVIP == true)
                .WhereIF(searchSpecialProductRules_In.SpecialProductRules == EnumSpecialProductRules.IsAllowGtisExportVIP, (u, prs) => prs.AllowGtisExportVIP == true)
                .WhereIF(searchSpecialProductRules_In.SpecialProductRules == EnumSpecialProductRules.IsAllowGtisExport, (u, prs) => prs.AllowGtisExport == true)
                .Where((u, prs) => u.OrganizationId != "********-0000-0000-0000-********0000")
                .Where((u, prs) => u.Deleted == false)
                .OrderByDescending((u, prs) => new { prs.AllowSuperSubAccount, prs.AllowGtisExport, prs.AllowGtisExportVIP, prs.AllowGtisImportVIP })
                .Select<SearchSpecialProductRules_Out>((u, prs) => new SearchSpecialProductRules_Out
                {
                    Id = prs.Id,
                    UserName = u.Name,
                    UserId = u.Id,
                    UserOrgId = u.OrganizationId,
                    UserOrgFullName = u.OrgFullName,
                    CreateUser = prs.CreateUser,
                    CreateDate = prs.CreateDate.ToString("yyyy-MM-dd HH:mm")
                }, true)
                .Mapper(it =>
                {
                    it.AllowSuperSubAccount = it.AllowSuperSubAccount == null ? false : it.AllowSuperSubAccount;
                    it.AllowGtisImportVIP = it.AllowGtisImportVIP == null ? false : it.AllowGtisImportVIP;
                    it.AllowGtisExportVIP = it.AllowGtisExportVIP == null ? false : it.AllowGtisExportVIP;
                    it.AllowGtisExport = it.AllowGtisExport == null ? false : it.AllowGtisExport;
                    it.CreateUserName = it.CreateUser == null ? null
                                                                : Db.Queryable<Db_sys_user>()
                                                                .Single(e => e.Id == it.CreateUser).Name;
                    it.CreateDate = it.CreateDate == null ? null : it.CreateDate;
                })
                .ToPageList(searchSpecialProductRules_In.PageNumber, searchSpecialProductRules_In.PageSize, ref total);
        }


        public ProductActionResult AddUserSpecialProductRules(AddUserSpecialProductRules addUserSpecialProductRules)
        {
            ProductActionResult actionResult = new ProductActionResult();
            actionResult.ResultCode = 0;

            var isHaveData = DbOpe_crm_product_rules_special.Instance.GetData(r => r.UserId == addUserSpecialProductRules.UserId);
            if (isHaveData.IsNotNull())
            {
                Db.Updateable<Db_crm_product_rules_special>()
                  .SetColumns(e => e.AllowSuperSubAccount == addUserSpecialProductRules.AllowSuperSubAccount)
                  .SetColumns(e => e.AllowGtisImportVIP == addUserSpecialProductRules.AllowGtisImportVIP)
                  .SetColumns(e => e.AllowGtisExportVIP == addUserSpecialProductRules.AllowGtisExportVIP)
                  .SetColumns(e => e.AllowGtisExport == addUserSpecialProductRules.AllowGtisExport)
                  .SetColumns(e => e.UpdateUser == UserId)
                  .SetColumns(e => e.UpdateDate == DateTime.Now)
                  .Where(e => e.UserId == addUserSpecialProductRules.UserId)
                  .ExecuteCommand();
            }
            else
            {
                Db_crm_product_rules_special newdata = new Db_crm_product_rules_special();
                newdata.Id = Guid.NewGuid().ToString();
                newdata.UserId = addUserSpecialProductRules.UserId;
                newdata.AllowSuperSubAccount = addUserSpecialProductRules.AllowSuperSubAccount;
                newdata.AllowGtisImportVIP = addUserSpecialProductRules.AllowGtisImportVIP;
                newdata.AllowGtisExportVIP = addUserSpecialProductRules.AllowGtisExportVIP;
                newdata.AllowGtisExport = addUserSpecialProductRules.AllowGtisExport;
                newdata.CreateDate = DateTime.Now;
                newdata.CreateUser = UserId;

                DbOpe_crm_product_rules_special.Instance.InsertQueue(newdata);

            }

            Instance.SaveQueues();




            return actionResult;
        }

        public List<SpecialProductRecorder> SearchUserSpecialProductRules(GetSpecialProductRecorderList_In getUserSpecialCollectingcompanyListIn, ref int total)
        {


            return
                Db.Queryable<Db_crm_product_rules_specialuseful>()
                .LeftJoin<Db_v_userwithorg>((e, u) => e.UserId == u.Id)
                .LeftJoin<Db_crm_contract>((e, u, c) => c.Id == e.ContractId)
                .Where((e, u, c) => e.Deleted == false && c.Deleted == false)
                .Where((e, u, c) => c.ContractStatus == 3)
                .WhereIF(getUserSpecialCollectingcompanyListIn.ContractName.IsNotNullOrEmpty(), (e, u, c) => c.ContractName.Contains(getUserSpecialCollectingcompanyListIn.ContractName))
                .WhereIF(getUserSpecialCollectingcompanyListIn.UserId.IsNotNullOrEmpty(), (e, u, c) => u.Name.Contains(getUserSpecialCollectingcompanyListIn.UserId))
                .WhereIF(getUserSpecialCollectingcompanyListIn.UserOrgId.IsNotNullOrEmpty(), (e, u, c) => u.OrganizationId == getUserSpecialCollectingcompanyListIn.UserOrgId)
                .WhereIF(getUserSpecialCollectingcompanyListIn.SpecialType.IsNotNull(), (e, u, c) => e.SpecialType == (int)getUserSpecialCollectingcompanyListIn.SpecialType)
                //.Select<SpecialProductRecorder>()
                .OrderByDescending(e => e.CreateDate)
                .Select((e, u, c) => new SpecialProductRecorder
                {
                    UserName = u.Name,
                    CreateTime = c.CreateDate.Value.ToString("yyyy-MM-dd HH:mm")
                }, true)
                .ToPageList(getUserSpecialCollectingcompanyListIn.PageNumber, getUserSpecialCollectingcompanyListIn.PageSize, ref total);
        }

        public List<Db_crm_product> GetProductList(Expression<Func<Db_crm_product, bool>> predicate)
        {
            return Queryable.Where(predicate).ToList();
        }

        public ProductActionResult AddUserSpecialCollectingcompany(AddUserSpecialCollectingcompany addUserSpecialCollectingcompany)
        {
            ProductActionResult actionResult = new ProductActionResult();
            actionResult.ResultCode = 0;

            var isHaveData = DbOpe_sys_collectingcompany_user_spacial.Instance.GetData(r => r.UserId == addUserSpecialCollectingcompany.UserId && r.CollectingCompanyId == addUserSpecialCollectingcompany.CollectingcompanyId);
            if (isHaveData.IsNotNull())
            {
                //Db.Updateable<Db_sys_collectingcompany_user_spacial>()
                //  .SetColumns(e => e.CollectingCompanyId == addUserSpecialCollectingcompany.CollectingcompanyId)
                //  .SetColumns(e => e.UpdateUser == UserId)
                //  .SetColumns(e => e.UpdateDate == DateTime.Now)
                //  .Where(e => e.UserId == addUserSpecialCollectingcompany.UserId && )
                //  .ExecuteCommand();
            }
            else
            {
                Db_sys_collectingcompany_user_spacial newdata = new Db_sys_collectingcompany_user_spacial();
                newdata.Id = Guid.NewGuid().ToString();
                newdata.UserId = addUserSpecialCollectingcompany.UserId;
                newdata.CollectingCompanyId = addUserSpecialCollectingcompany.CollectingcompanyId;
                newdata.ContractId = null;
                newdata.CreateDate = DateTime.Now;
                newdata.CreateUser = UserId;

                DbOpe_sys_collectingcompany_user_spacial.Instance.InsertQueue(newdata);
            }
            Instance.SaveQueues();
            return actionResult;
        }
        public ProductActionResult DeleteUserSpecialCollectingcompany(List<string> Ids)
        {
            ProductActionResult actionResult = new ProductActionResult();
            actionResult.ResultCode = 0;
            try
            {
                Db.Updateable<Db_sys_collectingcompany_user_spacial>()
                      .SetColumns(e => e.Deleted == true)
                      .Where(e => Ids.Contains(e.Id))
                      .ExecuteCommand();
                return actionResult;
            }
            catch (Exception ex)
            {
                actionResult.ResultMessage = ex.InnerException.Message;
                actionResult.ResultCode = -1;
                //throw new ApiException(actionResult.ResultMessage);
                return actionResult;
            }
        }

        public List<SearchUserSpecialCollectingcompany_Out> SearchUserSpecialCollectingcompany(GetUserSpecialCollectingcompanyList_In getUserSpecialCollectingcompanyListIn, ref int total)
        {

            return
                Db.Queryable<Db_sys_collectingcompany_user_spacial>()
                .LeftJoin<Db_v_userwithorg>((e, u) => e.UserId == u.Id)
                .LeftJoin<Db_crm_contract>((e, u, c) => c.Id == e.ContractId && c.ContractStatus == 3)
                .LeftJoin<Db_crm_collectingcompany>((e, u, c, cc) => cc.Id == e.CollectingCompanyId)
                .Where((e, u, c) => e.Deleted == false)
                //.Where((e,u, c) => c.ContractStatus == 3)
                .WhereIF(getUserSpecialCollectingcompanyListIn.ContractName.IsNotNullOrEmpty(), (e, u, c) => c.ContractName.Contains(getUserSpecialCollectingcompanyListIn.ContractName))
                .WhereIF(getUserSpecialCollectingcompanyListIn.UserId.IsNotNullOrEmpty(), (e, u, c) => u.Name.Contains(getUserSpecialCollectingcompanyListIn.UserId))
                .WhereIF(getUserSpecialCollectingcompanyListIn.UserOrgId.IsNotNullOrEmpty(), (e, u, c) => u.OrganizationId == getUserSpecialCollectingcompanyListIn.UserOrgId)
                .WhereIF(getUserSpecialCollectingcompanyListIn.CollectingcompanyId.IsNotNullOrEmpty(), (e, u, c, cc) => cc.Id == getUserSpecialCollectingcompanyListIn.CollectingcompanyId)
                //.Select<SpecialProductRecorder>()
                .OrderByDescending((e, u, c, cc) => e.CreateDate)
                .Select((e, u, c, cc) => new SearchUserSpecialCollectingcompany_Out
                {
                    Id = e.Id,
                    UserId = u.Id,
                    UserName = u.Name,
                    UserOrgFullName = u.OrgFullName,
                    CollectingcompanyIdName = cc.CollectingCompanyName,
                    CreateUser = e.CreateUser,
                    CreateDate = e.CreateDate.ToString("yyyy-MM-dd HH:mm")
                }, true)
                .Mapper(it =>
                {
                    it.CreateUserName = it.CreateUser == null ? null
                                                                : Db.Queryable<Db_sys_user>()
                                                                .Single(e => e.Id == it.CreateUser).Name;
                    it.CreateDate = it.CreateDate == null ? null : it.CreateDate;
                })
                .ToPageList(getUserSpecialCollectingcompanyListIn.PageNumber, getUserSpecialCollectingcompanyListIn.PageSize, ref total);
        }



    }
}
