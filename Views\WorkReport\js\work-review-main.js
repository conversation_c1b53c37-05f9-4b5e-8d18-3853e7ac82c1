/**
 * 工作回顾模块 - 主要JavaScript文件
 * 负责页面交互、数据加载和选项卡切换
 */

// 全局变量
let currentTab = 'summary';
let currentData = null;

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    // 等待布局初始化完成后再初始化工作回顾模块
    setTimeout(() => {
        initializePage();
    }, 100);
});

/**
 * 初始化页面
 */
function initializePage() {
    // 设置页面标题
    if (window.CRMLayout) {
        window.CRMLayout.setPageTitle('📊 工作回顾', '基于日报数据的团队工作量统计分析，支持多维度对比和趋势分析');
    }

    // 初始化时间范围标签
    updatePeriodLabels('thisMonth', true);

    // 绑定筛选条件变化事件
    bindFilterEvents();

    // 加载默认数据
    loadDefaultData();

    // 初始化概览卡片动画
    initializeOverviewCards();
}

/**
 * 绑定筛选条件事件
 */
function bindFilterEvents() {
    // 启用对比复选框变化事件
    document.getElementById('enableComparison').addEventListener('change', function() {
        const timeRange = document.getElementById('timeRange').value;
        updatePeriodLabels(timeRange, this.checked);
        toggleComparisonColumns(this.checked);
    });
    
    // 时间范围变化事件
    document.getElementById('timeRange').addEventListener('change', function() {
        const enableComparison = document.getElementById('enableComparison').checked;
        updatePeriodLabels(this.value, enableComparison);
    });
}

/**
 * 选项卡切换功能
 */
function switchTab(tabName) {
    // 移除所有选项卡的激活状态
    const tabItems = document.querySelectorAll('.tab-item');
    tabItems.forEach(item => item.classList.remove('active'));
    
    // 激活选中的选项卡
    event.target.classList.add('active');
    
    // 更新当前选项卡
    currentTab = tabName;
    
    // 加载对应的表格内容
    loadTableContent(tabName);
}

/**
 * 加载数据功能
 */
function loadData() {
    const filters = getFilterValues();

    // 显示加载状态
    showLoading();

    // 模拟API调用
    setTimeout(() => {
        try {
            // 更新时间范围显示
            updatePeriodLabels(filters.timeRange, filters.enableComparison);

            // 模拟API响应数据
            const mockData = generateMockData(filters);
            currentData = mockData;

            // 更新对比说明
            updateComparisonInfo(mockData.comparisonInfo, filters.enableComparison);

            // 更新概览卡片
            updateOverviewCards(mockData);

            // 加载当前选项卡的表格内容
            loadTableContent(currentTab);

            hideLoading();

            // 显示成功消息
            showSuccessMessage('数据加载完成');

            console.log('查询参数:', filters);
            console.log('返回数据:', mockData);
        } catch (error) {
            hideLoading();
            showErrorMessage('数据加载失败，请重试');
            console.error('数据加载错误:', error);
        }
    }, 1000);
}

/**
 * 获取筛选条件值
 */
function getFilterValues() {
    return {
        timeRange: document.getElementById('timeRange').value,
        department: document.getElementById('department').value,
        personnel: document.getElementById('personnel').value,
        enableComparison: document.getElementById('enableComparison').checked,
        onlyDirect: document.getElementById('onlyDirect').checked,
        comparisonMode: document.getElementById('comparisonMode')?.value || 'auto'
    };
}

/**
 * 生成模拟数据
 */
function generateMockData(filters) {
    // 获取当前日期信息
    const now = new Date();
    const currentPeriodInfo = getPeriodInfo(filters.timeRange, now);

    // 根据时间范围和用户选择的对比模式生成数据
    const comparisonMode = getComparisonMode(filters.timeRange, currentPeriodInfo, filters.comparisonMode);

    // 生成基础数据（这里应该是实际的API调用）
    const baseData = generateBaseData(currentPeriodInfo, comparisonMode);

    return {
        summary: baseData.summary,
        comparisonInfo: {
            mode: comparisonMode,
            currentPeriod: currentPeriodInfo.current,
            previousPeriod: currentPeriodInfo.previous,
            note: getComparisonNote(comparisonMode, currentPeriodInfo)
        },
        personnel: [
            {
                name: '张三',
                current: { phoneCount: 25, visitCount: 8, emailCount: 15, socialMediaCount: 45, newCustomerCount: 2, contractCustomerCount: 1 },
                previous: { phoneCount: 22, visitCount: 7, emailCount: 13, socialMediaCount: 42, newCustomerCount: 1, contractCustomerCount: 1 }
            },
            {
                name: '李四',
                current: { phoneCount: 18, visitCount: 6, emailCount: 12, socialMediaCount: 38, newCustomerCount: 1, contractCustomerCount: 1 },
                previous: { phoneCount: 16, visitCount: 5, emailCount: 10, socialMediaCount: 35, newCustomerCount: 1, contractCustomerCount: 1 }
            },
            {
                name: '王五',
                current: { phoneCount: 22, visitCount: 7, emailCount: 14, socialMediaCount: 42, newCustomerCount: 2, contractCustomerCount: 1 },
                previous: { phoneCount: 20, visitCount: 6, emailCount: 12, socialMediaCount: 40, newCustomerCount: 1, contractCustomerCount: 1 }
            }
        ],
        departments: [
            {
                name: '销售一部',
                userCount: 5,
                current: { phoneCount: 85, visitCount: 25, emailCount: 45, socialMediaCount: 125, newCustomerCount: 5, contractCustomerCount: 3 },
                previous: { phoneCount: 75, visitCount: 20, emailCount: 38, socialMediaCount: 108, newCustomerCount: 3, contractCustomerCount: 2 }
            },
            {
                name: '销售二部',
                userCount: 4,
                current: { phoneCount: 72, visitCount: 20, emailCount: 38, socialMediaCount: 108, newCustomerCount: 4, contractCustomerCount: 2 },
                previous: { phoneCount: 65, visitCount: 18, emailCount: 32, socialMediaCount: 95, newCustomerCount: 3, contractCustomerCount: 2 }
            },
            {
                name: '销售三部',
                userCount: 3,
                current: { phoneCount: 55, visitCount: 15, emailCount: 28, socialMediaCount: 85, newCustomerCount: 2, contractCustomerCount: 2 },
                previous: { phoneCount: 48, visitCount: 12, emailCount: 24, socialMediaCount: 78, newCustomerCount: 1, contractCustomerCount: 1 }
            },
            {
                name: '海外部',
                userCount: 2,
                current: { phoneCount: 35, visitCount: 10, emailCount: 18, socialMediaCount: 55, newCustomerCount: 1, contractCustomerCount: 3 },
                previous: { phoneCount: 30, visitCount: 8, emailCount: 15, socialMediaCount: 48, newCustomerCount: 1, contractCustomerCount: 1 }
            }
        ]
    };
}

/**
 * 导出数据功能
 */
function exportData() {
    const activeTabText = document.querySelector('.tab-item.active').textContent;
    
    // 显示导出提示
    showExportDialog(activeTabText);
    
    // 这里应该实现实际的导出功能
    console.log('导出数据:', currentTab, currentData);
}

/**
 * 显示导出对话框
 */
function showExportDialog(tabName) {
    if (confirm(`确定要导出 ${tabName} 数据吗？`)) {
        // 模拟导出过程
        alert('正在导出数据，请稍候...');
        
        setTimeout(() => {
            alert('数据导出完成！');
        }, 2000);
    }
}

/**
 * 更新时间范围标签
 */
function updatePeriodLabels(timeRange, enableComparison) {
    let currentPeriod, previousPeriod;
    
    switch(timeRange) {
        case 'thisWeek':
            currentPeriod = '本周';
            previousPeriod = '上周';
            break;
        case 'thisMonth':
            currentPeriod = '本月';
            previousPeriod = '上月';
            break;
        case 'thisQuarter':
            currentPeriod = '本季度';
            previousPeriod = '上季度';
            break;
        case 'thisYear':
            currentPeriod = '本年';
            previousPeriod = '去年';
            break;
    }
    
    const label = enableComparison ? `${currentPeriod} vs ${previousPeriod}` : currentPeriod;
    
    // 更新所有时间范围标签
    const periodElements = document.querySelectorAll('[id$="Period"]');
    periodElements.forEach(element => {
        if (element) {
            element.textContent = label;
        }
    });
}

/**
 * 显示加载状态
 */
function showLoading() {
    const container = document.getElementById('tableContainer');
    if (container) {
        container.innerHTML = '<div class="loading">正在加载数据...</div>';
    }

    // 同时显示页面级加载状态
    if (window.CRMLayout) {
        window.CRMLayout.showPageLoading();
    }
}

/**
 * 隐藏加载状态
 */
function hideLoading() {
    // 隐藏页面级加载状态
    if (window.CRMLayout) {
        window.CRMLayout.hidePageLoading();
    }
    // 表格加载状态会在loadTableContent中被替换
}

/**
 * 切换对比列显示/隐藏
 */
function toggleComparisonColumns(show) {
    // 重新加载当前表格以应用对比列的显示/隐藏
    loadTableContent(currentTab);
    
    console.log('切换对比列显示:', show);
}

/**
 * 加载默认数据
 */
function loadDefaultData() {
    // 生成默认数据
    const defaultFilters = getFilterValues();
    currentData = generateMockData(defaultFilters);
    
    // 加载默认表格内容
    loadTableContent('summary');
}

/**
 * 计算变化值和百分比
 */
function calculateChange(current, previous) {
    const change = current - previous;
    const percentage = previous === 0 ? 0 : ((change / previous) * 100);
    
    return {
        change: change,
        percentage: percentage,
        trend: change > 0 ? 'up' : (change < 0 ? 'down' : 'equal')
    };
}

/**
 * 格式化数字显示
 */
function formatNumber(num) {
    if (typeof num === 'string' && num.includes('%')) {
        return num;
    }
    return num.toLocaleString();
}

/**
 * 格式化百分比显示
 */
function formatPercentage(num) {
    return (num >= 0 ? '+' : '') + num.toFixed(1) + '%';
}

/**
 * 初始化概览卡片动画
 */
function initializeOverviewCards() {
    const cards = document.querySelectorAll('.overview-card');
    cards.forEach((card, index) => {
        // 添加延迟动画效果
        setTimeout(() => {
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);

        // 初始状态
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        card.style.transition = 'all 0.5s ease';
    });
}

/**
 * 更新概览卡片数据
 */
function updateOverviewCards(data) {
    if (!data || !data.summary) return;

    const cards = document.querySelectorAll('.overview-card');
    const summaryData = data.summary;

    // 定义卡片数据映射
    const cardMappings = [
        {
            selector: 0,
            currentKey: 'phoneCount',
            previousKey: 'phoneCount',
            label: '电话量'
        },
        {
            selector: 1,
            currentKey: 'visitCount',
            previousKey: 'visitCount',
            label: '拜访量'
        },
        {
            selector: 2,
            currentKey: 'socialMediaCount',
            previousKey: 'socialMediaCount',
            label: '社媒量'
        },
        {
            selector: 3,
            currentKey: 'newCustomerCount',
            previousKey: 'newCustomerCount',
            label: '新增客户'
        }
    ];

    cardMappings.forEach(mapping => {
        const card = cards[mapping.selector];
        if (!card) return;

        const current = summaryData.current[mapping.currentKey];
        const previous = summaryData.previous[mapping.previousKey];
        const change = calculateChange(current, previous);

        const valueElement = card.querySelector('.card-value');
        const trendElement = card.querySelector('.card-trend');

        if (valueElement) {
            // 数字动画效果
            animateNumber(valueElement, 0, current, 1000);
        }

        if (trendElement) {
            trendElement.textContent = formatPercentage(change.percentage);
            trendElement.className = `card-trend ${change.trend === 'up' ? 'positive' : 'negative'}`;
        }
    });
}

/**
 * 数字动画效果
 */
function animateNumber(element, start, end, duration) {
    const startTime = performance.now();

    function update(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);

        // 使用缓动函数
        const easeOutQuart = 1 - Math.pow(1 - progress, 4);
        const current = Math.round(start + (end - start) * easeOutQuart);

        element.textContent = current;

        if (progress < 1) {
            requestAnimationFrame(update);
        }
    }

    requestAnimationFrame(update);
}

/**
 * 显示成功消息
 */
function showSuccessMessage(message) {
    if (window.CRMLayout) {
        window.CRMLayout.showMessage(message, 'success');
    } else {
        alert(message);
    }
}

/**
 * 显示错误消息
 */
function showErrorMessage(message) {
    if (window.CRMLayout) {
        window.CRMLayout.showMessage(message, 'error');
    } else {
        alert(message);
    }
}

/**
 * 获取时间段信息
 */
function getPeriodInfo(timeRange, currentDate) {
    const now = new Date(currentDate);
    let currentStart, currentEnd, previousStart, previousEnd;

    switch(timeRange) {
        case 'thisWeek':
            // 本周：周一到今天
            const dayOfWeek = now.getDay() || 7; // 周日为7
            currentStart = new Date(now);
            currentStart.setDate(now.getDate() - dayOfWeek + 1);
            currentEnd = new Date(now);

            // 上周：完整的上周
            previousEnd = new Date(currentStart);
            previousEnd.setDate(previousEnd.getDate() - 1);
            previousStart = new Date(previousEnd);
            previousStart.setDate(previousEnd.getDate() - 6);
            break;

        case 'thisMonth':
            // 本月：1号到今天
            currentStart = new Date(now.getFullYear(), now.getMonth(), 1);
            currentEnd = new Date(now);

            // 上月：完整的上月
            previousStart = new Date(now.getFullYear(), now.getMonth() - 1, 1);
            previousEnd = new Date(now.getFullYear(), now.getMonth(), 0);
            break;

        case 'thisQuarter':
            // 本季度：季度开始到今天
            const quarter = Math.floor(now.getMonth() / 3);
            currentStart = new Date(now.getFullYear(), quarter * 3, 1);
            currentEnd = new Date(now);

            // 上季度：完整的上季度
            const prevQuarter = quarter === 0 ? 3 : quarter - 1;
            const prevYear = quarter === 0 ? now.getFullYear() - 1 : now.getFullYear();
            previousStart = new Date(prevYear, prevQuarter * 3, 1);
            previousEnd = new Date(prevYear, prevQuarter * 3 + 3, 0);
            break;

        case 'thisYear':
            // 本年：1月1日到今天
            currentStart = new Date(now.getFullYear(), 0, 1);
            currentEnd = new Date(now);

            // 去年：完整的去年
            previousStart = new Date(now.getFullYear() - 1, 0, 1);
            previousEnd = new Date(now.getFullYear() - 1, 11, 31);
            break;
    }

    return {
        current: {
            start: currentStart,
            end: currentEnd,
            days: Math.ceil((currentEnd - currentStart) / (1000 * 60 * 60 * 24)) + 1
        },
        previous: {
            start: previousStart,
            end: previousEnd,
            days: Math.ceil((previousEnd - previousStart) / (1000 * 60 * 60 * 24)) + 1
        }
    };
}

/**
 * 获取对比模式
 */
function getComparisonMode(timeRange, periodInfo, userSelectedMode = 'auto') {
    // 如果用户手动选择了对比模式，直接使用
    if (userSelectedMode !== 'auto') {
        return userSelectedMode;
    }

    const currentDays = periodInfo.current.days;
    const previousDays = periodInfo.previous.days;

    // 智能判断对比模式
    if (currentDays < previousDays * 0.3) {
        // 当前时间段不足对比时间段的30%，使用同期对比
        return 'same_period';
    }

    if (currentDays > previousDays * 0.8) {
        // 当前时间段超过对比时间段的80%，使用完整对比
        return 'full_period';
    }

    // 否则使用日均对比
    return 'daily_average';
}

/**
 * 生成基础数据
 */
function generateBaseData(periodInfo, comparisonMode) {
    // 模拟当前时间段的实际数据
    const currentData = {
        phoneCount: 156,
        visitCount: 45,
        emailPreciseCount: 89,
        emailBulkCount: 67,
        socialMediaCount: 234,
        replyCount: 123,
        demoCount: 23,
        newCustomerCount: 25,
        followCustomerCount: 78,
        contractCustomerCount: 12,
        reportCount: 89,
        onTimeRate: 85.4
    };

    // 模拟对比时间段的数据
    let previousData;

    switch(comparisonMode) {
        case 'same_period':
            // 同期对比：取上期相同天数的数据
            previousData = {
                phoneCount: Math.round(142 * periodInfo.current.days / periodInfo.previous.days),
                visitCount: Math.round(38 * periodInfo.current.days / periodInfo.previous.days),
                emailPreciseCount: Math.round(76 * periodInfo.current.days / periodInfo.previous.days),
                emailBulkCount: Math.round(58 * periodInfo.current.days / periodInfo.previous.days),
                socialMediaCount: Math.round(198 * periodInfo.current.days / periodInfo.previous.days),
                replyCount: Math.round(115 * periodInfo.current.days / periodInfo.previous.days),
                demoCount: Math.round(19 * periodInfo.current.days / periodInfo.previous.days),
                newCustomerCount: Math.round(18 * periodInfo.current.days / periodInfo.previous.days),
                followCustomerCount: Math.round(65 * periodInfo.current.days / periodInfo.previous.days),
                contractCustomerCount: Math.round(8 * periodInfo.current.days / periodInfo.previous.days),
                reportCount: Math.round(82 * periodInfo.current.days / periodInfo.previous.days),
                onTimeRate: 82.9
            };
            break;

        case 'daily_average':
            // 日均对比：当前日均 vs 上期日均
            const currentDaily = {};
            const previousDaily = {};

            Object.keys(currentData).forEach(key => {
                if (key !== 'onTimeRate') {
                    currentDaily[key] = Math.round(currentData[key] / periodInfo.current.days * 10) / 10;
                    previousDaily[key] = Math.round(142 / periodInfo.previous.days * 10) / 10; // 使用模拟数据
                } else {
                    currentDaily[key] = currentData[key];
                    previousDaily[key] = 82.9;
                }
            });

            return {
                summary: {
                    current: currentDaily,
                    previous: previousDaily
                }
            };

        case 'full_period':
        default:
            // 完整对比：当前累计 vs 上期完整
            previousData = {
                phoneCount: 142,
                visitCount: 38,
                emailPreciseCount: 76,
                emailBulkCount: 58,
                socialMediaCount: 198,
                replyCount: 115,
                demoCount: 19,
                newCustomerCount: 18,
                followCustomerCount: 65,
                contractCustomerCount: 8,
                reportCount: 82,
                onTimeRate: 82.9
            };
            break;
    }

    return {
        summary: {
            current: currentData,
            previous: previousData
        }
    };
}

/**
 * 获取对比说明
 */
function getComparisonNote(comparisonMode, periodInfo) {
    const currentDays = periodInfo.current.days;
    const previousDays = periodInfo.previous.days;
    const currentStart = periodInfo.current.start.toLocaleDateString();
    const currentEnd = periodInfo.current.end.toLocaleDateString();
    const previousStart = periodInfo.previous.start.toLocaleDateString();
    const previousEnd = periodInfo.previous.end.toLocaleDateString();

    switch(comparisonMode) {
        case 'same_period':
            return `同期对比模式：当前时间段${currentDays}天（${currentStart} ~ ${currentEnd}）与上期相同天数进行对比。适用于月初、季初等不完整时间段的对比分析。`;
        case 'daily_average':
            return `日均对比模式：当前日均工作量与上期日均工作量对比。消除时间段长度差异，更准确反映工作效率变化趋势。`;
        case 'full_period':
            return `完整对比模式：当前${currentDays}天累计数据（${currentStart} ~ ${currentEnd}）与上期完整${previousDays}天数据（${previousStart} ~ ${previousEnd}）对比。`;
        default:
            return '数据对比分析';
    }
}

/**
 * 更新对比说明显示
 */
function updateComparisonInfo(comparisonInfo, enableComparison) {
    const comparisonInfoElement = document.getElementById('comparisonInfo');
    const comparisonNoteElement = document.getElementById('comparisonNote');

    if (!comparisonInfoElement || !comparisonNoteElement) return;

    if (enableComparison && comparisonInfo) {
        comparisonInfoElement.style.display = 'block';
        comparisonNoteElement.textContent = comparisonInfo.note;

        // 添加淡入动画
        comparisonInfoElement.style.opacity = '0';
        setTimeout(() => {
            comparisonInfoElement.style.opacity = '1';
            comparisonInfoElement.style.transition = 'opacity 0.3s ease';
        }, 100);
    } else {
        comparisonInfoElement.style.display = 'none';
    }
}
