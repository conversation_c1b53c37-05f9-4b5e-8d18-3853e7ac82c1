/**
 * 工作回顾模块 - 主要JavaScript文件
 * 负责页面交互、数据加载和选项卡切换
 */

// 全局变量
let currentTab = 'summary';
let currentData = null;

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    // 等待布局初始化完成后再初始化工作回顾模块
    setTimeout(() => {
        initializePage();
    }, 100);
});

/**
 * 初始化页面
 */
function initializePage() {
    // 设置页面标题
    if (window.CRMLayout) {
        window.CRMLayout.setPageTitle('📊 工作回顾', '基于日报数据的团队工作量统计分析，支持多维度对比和趋势分析');
    }

    // 设置默认对比模式
    setDefaultComparisonMode();

    // 初始化时间范围标签
    updatePeriodLabels('thisMonth', true);

    // 绑定筛选条件变化事件
    bindFilterEvents();

    // 加载默认数据
    loadDefaultData();

    // 初始化概览卡片动画
    initializeOverviewCards();
}

/**
 * 绑定筛选条件事件
 */
function bindFilterEvents() {
    // 启用对比复选框变化事件
    document.getElementById('enableComparison').addEventListener('change', function() {
        const timeRange = document.getElementById('timeRange').value;
        updatePeriodLabels(timeRange, this.checked);
        toggleComparisonColumns(this.checked);
    });

    // 时间范围变化事件
    document.getElementById('timeRange').addEventListener('change', function() {
        const enableComparison = document.getElementById('enableComparison').checked;

        // 处理自定义时间段的显示/隐藏
        handleCustomDateRange(this.value);

        updatePeriodLabels(this.value, enableComparison);

        // 时间范围变化时，自动更新推荐的对比模式
        updateRecommendedComparisonMode(this.value);
    });

    // 自定义日期变化事件
    const customStartDate = document.getElementById('customStartDate');
    const customEndDate = document.getElementById('customEndDate');

    if (customStartDate) {
        customStartDate.addEventListener('change', function() {
            validateCustomDateRange();
        });
    }

    if (customEndDate) {
        customEndDate.addEventListener('change', function() {
            validateCustomDateRange();
        });
    }

    // 对比模式变化事件
    document.getElementById('comparisonMode')?.addEventListener('change', function() {
        console.log(`对比模式切换为: ${this.value}`);
        // 可以在这里添加模式切换的提示或说明更新
    });

    // 统计模式变化事件
    document.getElementById('statisticsMode')?.addEventListener('change', function() {
        const modeNames = {
            '1': '按当前组织架构',
            '2': '按报告提交时团队',
            '3': '混合模式'
        };
        console.log(`统计模式切换为: ${modeNames[this.value]}`);

        // 显示模式切换提示
        if (window.CRMLayout) {
            window.CRMLayout.showMessage(`已切换到${modeNames[this.value]}`, 'info');
        }
    });
}

/**
 * 更新推荐的对比模式
 */
function updateRecommendedComparisonMode(timeRange) {
    const comparisonModeSelect = document.getElementById('comparisonMode');
    if (!comparisonModeSelect) return;

    // 获取当前时间信息
    const now = new Date();
    const periodInfo = getPeriodInfo(timeRange, now);
    const recommendedMode = getDefaultComparisonMode(timeRange, periodInfo);

    // 如果当前选择的模式不是推荐模式，可以提示用户
    const currentMode = comparisonModeSelect.value;
    if (currentMode !== recommendedMode) {
        console.log(`当前模式: ${currentMode}, 推荐模式: ${recommendedMode}`);

        // 可以选择自动切换或者只是提示
        // 这里选择自动切换到推荐模式
        comparisonModeSelect.value = recommendedMode;

        // 显示切换提示
        if (window.CRMLayout) {
            const modeNames = {
                'same_period': '同期对比',
                'daily_average': '日均对比',
                'full_period': '完整对比'
            };
            window.CRMLayout.showMessage(`已自动切换到推荐的${modeNames[recommendedMode]}模式`, 'info');
        }
    }
}

/**
 * 选项卡切换功能
 */
function switchTab(tabName) {
    // 移除所有选项卡的激活状态
    const tabItems = document.querySelectorAll('.tab-item');
    tabItems.forEach(item => item.classList.remove('active'));
    
    // 激活选中的选项卡
    event.target.classList.add('active');
    
    // 更新当前选项卡
    currentTab = tabName;
    
    // 加载对应的表格内容
    loadTableContent(tabName);
}

/**
 * 加载数据功能
 */
function loadData() {
    const filters = getFilterValues();

    // 如果是自定义时间段，先验证日期
    if (filters.timeRange === 'custom') {
        if (!validateCustomDateRange()) {
            return; // 验证失败，不继续加载
        }

        if (!filters.customStartDate || !filters.customEndDate) {
            showErrorMessage('请选择开始日期和结束日期');
            return;
        }
    }

    // 显示加载状态
    showLoading();

    // 模拟API调用
    setTimeout(() => {
        try {
            // 更新时间范围显示
            updatePeriodLabels(filters.timeRange, filters.enableComparison);

            // 模拟API响应数据
            const mockData = generateMockData(filters);
            currentData = mockData;

            // 更新时间段显示
            updateTimePeriodDisplay(mockData.comparisonInfo, filters.enableComparison);

            // 更新对比说明
            updateComparisonInfo(mockData.comparisonInfo, filters.enableComparison);

            // 更新概览卡片
            updateOverviewCards(mockData);

            // 加载当前选项卡的表格内容
            loadTableContent(currentTab);

            hideLoading();

            // 显示成功消息
            showSuccessMessage('数据加载完成');

            console.log('查询参数:', filters);
            console.log('返回数据:', mockData);
        } catch (error) {
            hideLoading();
            showErrorMessage('数据加载失败，请重试');
            console.error('数据加载错误:', error);
        }
    }, 1000);
}

/**
 * 设置默认对比模式
 */
function setDefaultComparisonMode() {
    const comparisonModeSelect = document.getElementById('comparisonMode');
    if (!comparisonModeSelect) return;

    // 获取当前时间信息，判断应该使用哪种对比模式
    const now = new Date();
    const timeRange = document.getElementById('timeRange').value;
    const periodInfo = getPeriodInfo(timeRange, now);
    const defaultMode = getDefaultComparisonMode(timeRange, periodInfo);

    // 设置默认选中的对比模式
    comparisonModeSelect.value = defaultMode;

    console.log(`默认对比模式设置为: ${defaultMode}`);
}

/**
 * 获取默认对比模式（原智能判断逻辑）
 */
function getDefaultComparisonMode(timeRange, periodInfo) {
    const currentDays = periodInfo.current.days;
    const previousDays = periodInfo.previous.days;

    // 智能判断默认对比模式
    if (currentDays < previousDays * 0.3) {
        // 当前时间段不足对比时间段的30%，默认使用同期对比
        return 'same_period';
    }

    if (currentDays > previousDays * 0.8) {
        // 当前时间段超过对比时间段的80%，默认使用完整对比
        return 'full_period';
    }

    // 否则默认使用日均对比
    return 'daily_average';
}

/**
 * 处理自定义时间段的显示/隐藏
 */
function handleCustomDateRange(timeRange) {
    const customDateGroups = document.querySelectorAll('.custom-date-group');

    if (timeRange === 'custom') {
        // 显示自定义日期选择器
        customDateGroups.forEach(group => {
            group.style.display = 'flex';
        });

        // 设置默认日期（本月第一天到今天）
        setDefaultCustomDates();
    } else {
        // 隐藏自定义日期选择器
        customDateGroups.forEach(group => {
            group.style.display = 'none';
        });
    }
}

/**
 * 设置默认的自定义日期
 */
function setDefaultCustomDates() {
    const today = new Date();
    const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);

    const customStartDate = document.getElementById('customStartDate');
    const customEndDate = document.getElementById('customEndDate');

    if (customStartDate && !customStartDate.value) {
        customStartDate.value = formatDateForInput(firstDayOfMonth);
    }

    if (customEndDate && !customEndDate.value) {
        customEndDate.value = formatDateForInput(today);
    }
}

/**
 * 验证自定义日期范围
 */
function validateCustomDateRange() {
    const customStartDate = document.getElementById('customStartDate');
    const customEndDate = document.getElementById('customEndDate');

    if (!customStartDate || !customEndDate) return true;

    const startDate = new Date(customStartDate.value);
    const endDate = new Date(customEndDate.value);

    if (startDate > endDate) {
        showErrorMessage('开始日期不能晚于结束日期');
        return false;
    }

    // 检查日期范围是否过长（超过1年）
    const daysDiff = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24));
    if (daysDiff > 365) {
        showErrorMessage('时间范围不能超过1年');
        return false;
    }

    return true;
}

/**
 * 格式化日期为input[type="date"]格式
 */
function formatDateForInput(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
}

/**
 * 获取筛选条件值
 */
function getFilterValues() {
    const timeRange = document.getElementById('timeRange').value;
    const filters = {
        timeRange: timeRange,
        department: document.getElementById('department').value,
        personnel: document.getElementById('personnel').value,
        enableComparison: document.getElementById('enableComparison').checked,
        onlyDirect: document.getElementById('onlyDirect').checked,
        comparisonMode: document.getElementById('comparisonMode')?.value || 'same_period',
        statisticsMode: document.getElementById('statisticsMode')?.value || '1'
    };

    // 如果是自定义时间段，添加自定义日期
    if (timeRange === 'custom') {
        filters.customStartDate = document.getElementById('customStartDate')?.value;
        filters.customEndDate = document.getElementById('customEndDate')?.value;
    }

    return filters;
}

/**
 * 生成模拟数据
 */
function generateMockData(filters) {
    // 获取当前日期信息
    const now = new Date();
    const currentPeriodInfo = getPeriodInfo(
        filters.timeRange,
        now,
        filters.customStartDate,
        filters.customEndDate
    );

    // 根据时间范围和用户选择的对比模式生成数据
    const comparisonMode = getComparisonMode(filters.timeRange, currentPeriodInfo, filters.comparisonMode);

    // 生成基础数据（这里应该是实际的API调用）
    const baseData = generateBaseData(currentPeriodInfo, comparisonMode);

    return {
        summary: baseData.summary,
        comparisonInfo: {
            mode: comparisonMode,
            currentPeriod: currentPeriodInfo.current,
            previousPeriod: currentPeriodInfo.previous,
            note: getComparisonNote(comparisonMode, currentPeriodInfo)
        },
        personnel: generatePersonnelData(filters.statisticsMode),
        departments: generateDepartmentData(filters.statisticsMode)
    };
}

/**
 * 导出数据功能
 */
function exportData() {
    const activeTabText = document.querySelector('.tab-item.active').textContent;
    
    // 显示导出提示
    showExportDialog(activeTabText);
    
    // 这里应该实现实际的导出功能
    console.log('导出数据:', currentTab, currentData);
}

/**
 * 显示导出对话框
 */
function showExportDialog(tabName) {
    if (confirm(`确定要导出 ${tabName} 数据吗？`)) {
        // 模拟导出过程
        alert('正在导出数据，请稍候...');
        
        setTimeout(() => {
            alert('数据导出完成！');
        }, 2000);
    }
}

/**
 * 更新时间范围标签
 */
function updatePeriodLabels(timeRange, enableComparison) {
    let currentPeriod, previousPeriod;

    switch(timeRange) {
        case 'thisWeek':
            currentPeriod = '本周';
            previousPeriod = '上周';
            break;
        case 'thisMonth':
            currentPeriod = '本月';
            previousPeriod = '上月';
            break;
        case 'thisQuarter':
            currentPeriod = '本季度';
            previousPeriod = '上季度';
            break;
        case 'thisYear':
            currentPeriod = '本年';
            previousPeriod = '去年';
            break;
        case 'custom':
            const customStartDate = document.getElementById('customStartDate')?.value;
            const customEndDate = document.getElementById('customEndDate')?.value;

            if (customStartDate && customEndDate) {
                const startDate = new Date(customStartDate);
                const endDate = new Date(customEndDate);
                currentPeriod = `${formatDateDisplay(startDate)} ~ ${formatDateDisplay(endDate)}`;

                // 计算对比时间段
                const daysDiff = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24));
                const prevEnd = new Date(startDate);
                prevEnd.setDate(prevEnd.getDate() - 1);
                const prevStart = new Date(prevEnd);
                prevStart.setDate(prevStart.getDate() - daysDiff);
                previousPeriod = `${formatDateDisplay(prevStart)} ~ ${formatDateDisplay(prevEnd)}`;
            } else {
                currentPeriod = '自定义时间段';
                previousPeriod = '对比时间段';
            }
            break;
    }

    const label = enableComparison ? `${currentPeriod} vs ${previousPeriod}` : currentPeriod;

    // 更新所有时间范围标签
    const periodElements = document.querySelectorAll('[id$="Period"]');
    periodElements.forEach(element => {
        if (element) {
            element.textContent = label;
        }
    });
}

/**
 * 格式化日期显示
 */
function formatDateDisplay(date) {
    const month = date.getMonth() + 1;
    const day = date.getDate();
    return `${month}月${day}日`;
}

/**
 * 显示加载状态
 */
function showLoading() {
    const container = document.getElementById('tableContainer');
    if (container) {
        container.innerHTML = '<div class="loading">正在加载数据...</div>';
    }

    // 同时显示页面级加载状态
    if (window.CRMLayout) {
        window.CRMLayout.showPageLoading();
    }
}

/**
 * 隐藏加载状态
 */
function hideLoading() {
    // 隐藏页面级加载状态
    if (window.CRMLayout) {
        window.CRMLayout.hidePageLoading();
    }
    // 表格加载状态会在loadTableContent中被替换
}

/**
 * 切换对比列显示/隐藏
 */
function toggleComparisonColumns(show) {
    // 重新加载当前表格以应用对比列的显示/隐藏
    loadTableContent(currentTab);
    
    console.log('切换对比列显示:', show);
}

/**
 * 加载默认数据
 */
function loadDefaultData() {
    // 生成默认数据
    const defaultFilters = getFilterValues();
    currentData = generateMockData(defaultFilters);

    // 更新时间段显示
    updateTimePeriodDisplay(currentData.comparisonInfo, defaultFilters.enableComparison);

    // 更新对比说明
    updateComparisonInfo(currentData.comparisonInfo, defaultFilters.enableComparison);

    // 更新概览卡片
    updateOverviewCards(currentData);

    // 加载默认表格内容
    loadTableContent('summary');
}

/**
 * 计算变化值和百分比
 */
function calculateChange(current, previous) {
    const change = current - previous;
    const percentage = previous === 0 ? 0 : ((change / previous) * 100);
    
    return {
        change: change,
        percentage: percentage,
        trend: change > 0 ? 'up' : (change < 0 ? 'down' : 'equal')
    };
}

/**
 * 格式化数字显示
 */
function formatNumber(num) {
    if (typeof num === 'string' && num.includes('%')) {
        return num;
    }
    return num.toLocaleString();
}

/**
 * 格式化百分比显示
 */
function formatPercentage(num) {
    return (num >= 0 ? '+' : '') + num.toFixed(1) + '%';
}

/**
 * 初始化概览卡片动画
 */
function initializeOverviewCards() {
    const cards = document.querySelectorAll('.overview-card');
    cards.forEach((card, index) => {
        // 添加延迟动画效果
        setTimeout(() => {
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);

        // 初始状态
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        card.style.transition = 'all 0.5s ease';
    });
}

/**
 * 更新概览卡片数据
 */
function updateOverviewCards(data) {
    if (!data || !data.summary) return;

    const cards = document.querySelectorAll('.overview-card');
    const summaryData = data.summary;

    // 定义卡片数据映射
    const cardMappings = [
        {
            selector: 0,
            currentKey: 'phoneCount',
            previousKey: 'phoneCount',
            label: '电话量'
        },
        {
            selector: 1,
            currentKey: 'visitCount',
            previousKey: 'visitCount',
            label: '拜访量'
        },
        {
            selector: 2,
            currentKey: 'socialMediaCount',
            previousKey: 'socialMediaCount',
            label: '社媒量'
        },
        {
            selector: 3,
            currentKey: 'newCustomerCount',
            previousKey: 'newCustomerCount',
            label: '新增客户'
        }
    ];

    cardMappings.forEach(mapping => {
        const card = cards[mapping.selector];
        if (!card) return;

        const current = summaryData.current[mapping.currentKey];
        const previous = summaryData.previous[mapping.previousKey];
        const change = calculateChange(current, previous);

        const valueElement = card.querySelector('.card-value');
        const trendElement = card.querySelector('.card-trend');

        if (valueElement) {
            // 数字动画效果
            animateNumber(valueElement, 0, current, 1000);
        }

        if (trendElement) {
            trendElement.textContent = formatPercentage(change.percentage);
            trendElement.className = `card-trend ${change.trend === 'up' ? 'positive' : 'negative'}`;
        }
    });
}

/**
 * 数字动画效果
 */
function animateNumber(element, start, end, duration) {
    const startTime = performance.now();

    function update(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);

        // 使用缓动函数
        const easeOutQuart = 1 - Math.pow(1 - progress, 4);
        const current = Math.round(start + (end - start) * easeOutQuart);

        element.textContent = current;

        if (progress < 1) {
            requestAnimationFrame(update);
        }
    }

    requestAnimationFrame(update);
}

/**
 * 显示成功消息
 */
function showSuccessMessage(message) {
    if (window.CRMLayout) {
        window.CRMLayout.showMessage(message, 'success');
    } else {
        alert(message);
    }
}

/**
 * 显示错误消息
 */
function showErrorMessage(message) {
    if (window.CRMLayout) {
        window.CRMLayout.showMessage(message, 'error');
    } else {
        alert(message);
    }
}

/**
 * 根据统计模式生成人员数据
 */
function generatePersonnelData(statisticsMode) {
    const basePersonnelData = {
        '1': [ // 按当前组织架构
            {
                name: '张三',
                department: '销售一部',
                position: '销售经理',
                current: { phoneCount: 25, visitCount: 8, emailCount: 15, socialMediaCount: 45, newCustomerCount: 2, contractCustomerCount: 1 },
                previous: { phoneCount: 22, visitCount: 7, emailCount: 13, socialMediaCount: 42, newCustomerCount: 1, contractCustomerCount: 1 }
            },
            {
                name: '李四',
                department: '销售一部',
                position: '销售专员',
                current: { phoneCount: 18, visitCount: 6, emailCount: 12, socialMediaCount: 38, newCustomerCount: 1, contractCustomerCount: 1 },
                previous: { phoneCount: 16, visitCount: 5, emailCount: 10, socialMediaCount: 35, newCustomerCount: 1, contractCustomerCount: 1 }
            },
            {
                name: '王五',
                department: '销售二部',
                position: '销售经理',
                current: { phoneCount: 22, visitCount: 7, emailCount: 14, socialMediaCount: 42, newCustomerCount: 2, contractCustomerCount: 1 },
                previous: { phoneCount: 20, visitCount: 6, emailCount: 12, socialMediaCount: 40, newCustomerCount: 1, contractCustomerCount: 1 }
            },
            {
                name: '赵六',
                department: '销售二部',
                position: '销售专员',
                current: { phoneCount: 15, visitCount: 5, emailCount: 10, socialMediaCount: 32, newCustomerCount: 1, contractCustomerCount: 0 },
                previous: { phoneCount: 14, visitCount: 4, emailCount: 9, socialMediaCount: 30, newCustomerCount: 1, contractCustomerCount: 1 }
            }
        ],
        '2': [ // 按报告提交时团队
            {
                name: '张三',
                department: '销售一部(历史)',
                position: '销售经理',
                current: { phoneCount: 28, visitCount: 9, emailCount: 16, socialMediaCount: 48, newCustomerCount: 3, contractCustomerCount: 2 },
                previous: { phoneCount: 24, visitCount: 8, emailCount: 14, socialMediaCount: 44, newCustomerCount: 2, contractCustomerCount: 1 }
            },
            {
                name: '李四',
                department: '销售三部(历史)',
                position: '销售专员',
                current: { phoneCount: 20, visitCount: 7, emailCount: 13, socialMediaCount: 40, newCustomerCount: 2, contractCustomerCount: 1 },
                previous: { phoneCount: 18, visitCount: 6, emailCount: 11, socialMediaCount: 37, newCustomerCount: 1, contractCustomerCount: 1 }
            },
            {
                name: '王五',
                department: '销售二部(历史)',
                position: '销售经理',
                current: { phoneCount: 19, visitCount: 6, emailCount: 12, socialMediaCount: 38, newCustomerCount: 1, contractCustomerCount: 1 },
                previous: { phoneCount: 17, visitCount: 5, emailCount: 10, socialMediaCount: 35, newCustomerCount: 1, contractCustomerCount: 0 }
            }
        ],
        '3': [ // 混合模式
            {
                name: '张三',
                department: '销售一部',
                position: '销售经理',
                note: '当前组织',
                current: { phoneCount: 25, visitCount: 8, emailCount: 15, socialMediaCount: 45, newCustomerCount: 2, contractCustomerCount: 1 },
                previous: { phoneCount: 22, visitCount: 7, emailCount: 13, socialMediaCount: 42, newCustomerCount: 1, contractCustomerCount: 1 }
            },
            {
                name: '李四',
                department: '销售三部(历史)',
                position: '销售专员',
                note: '历史团队',
                current: { phoneCount: 20, visitCount: 7, emailCount: 13, socialMediaCount: 40, newCustomerCount: 2, contractCustomerCount: 1 },
                previous: { phoneCount: 18, visitCount: 6, emailCount: 11, socialMediaCount: 37, newCustomerCount: 1, contractCustomerCount: 1 }
            },
            {
                name: '王五',
                department: '销售二部',
                position: '销售经理',
                note: '当前组织',
                current: { phoneCount: 22, visitCount: 7, emailCount: 14, socialMediaCount: 42, newCustomerCount: 2, contractCustomerCount: 1 },
                previous: { phoneCount: 20, visitCount: 6, emailCount: 12, socialMediaCount: 40, newCustomerCount: 1, contractCustomerCount: 1 }
            },
            {
                name: '钱七',
                department: '海外部(历史)',
                position: '海外专员',
                note: '历史团队',
                current: { phoneCount: 12, visitCount: 3, emailCount: 8, socialMediaCount: 25, newCustomerCount: 1, contractCustomerCount: 0 },
                previous: { phoneCount: 10, visitCount: 2, emailCount: 6, socialMediaCount: 22, newCustomerCount: 0, contractCustomerCount: 0 }
            }
        ]
    };

    return basePersonnelData[statisticsMode] || basePersonnelData['1'];
}

/**
 * 根据统计模式生成部门数据
 */
function generateDepartmentData(statisticsMode) {
    const baseDepartmentData = {
        '1': [ // 按当前组织架构
            {
                name: '销售一部',
                userCount: 5,
                current: { phoneCount: 85, visitCount: 25, emailCount: 45, socialMediaCount: 125, newCustomerCount: 5, contractCustomerCount: 3 },
                previous: { phoneCount: 75, visitCount: 20, emailCount: 38, socialMediaCount: 108, newCustomerCount: 3, contractCustomerCount: 2 }
            },
            {
                name: '销售二部',
                userCount: 4,
                current: { phoneCount: 72, visitCount: 20, emailCount: 38, socialMediaCount: 108, newCustomerCount: 4, contractCustomerCount: 2 },
                previous: { phoneCount: 65, visitCount: 18, emailCount: 32, socialMediaCount: 95, newCustomerCount: 3, contractCustomerCount: 2 }
            },
            {
                name: '销售三部',
                userCount: 3,
                current: { phoneCount: 55, visitCount: 15, emailCount: 28, socialMediaCount: 85, newCustomerCount: 2, contractCustomerCount: 2 },
                previous: { phoneCount: 48, visitCount: 12, emailCount: 24, socialMediaCount: 78, newCustomerCount: 1, contractCustomerCount: 1 }
            },
            {
                name: '海外部',
                userCount: 2,
                current: { phoneCount: 35, visitCount: 10, emailCount: 18, socialMediaCount: 55, newCustomerCount: 1, contractCustomerCount: 3 },
                previous: { phoneCount: 30, visitCount: 8, emailCount: 15, socialMediaCount: 48, newCustomerCount: 1, contractCustomerCount: 1 }
            }
        ],
        '2': [ // 按报告提交时团队
            {
                name: '销售一部(历史)',
                userCount: 6,
                current: { phoneCount: 95, visitCount: 28, emailCount: 52, socialMediaCount: 140, newCustomerCount: 6, contractCustomerCount: 4 },
                previous: { phoneCount: 85, visitCount: 24, emailCount: 45, socialMediaCount: 125, newCustomerCount: 4, contractCustomerCount: 3 }
            },
            {
                name: '销售二部(历史)',
                userCount: 3,
                current: { phoneCount: 58, visitCount: 16, emailCount: 30, socialMediaCount: 88, newCustomerCount: 3, contractCustomerCount: 2 },
                previous: { phoneCount: 52, visitCount: 14, emailCount: 26, socialMediaCount: 78, newCustomerCount: 2, contractCustomerCount: 1 }
            },
            {
                name: '销售三部(历史)',
                userCount: 4,
                current: { phoneCount: 68, visitCount: 18, emailCount: 35, socialMediaCount: 102, newCustomerCount: 4, contractCustomerCount: 2 },
                previous: { phoneCount: 60, visitCount: 15, emailCount: 30, socialMediaCount: 90, newCustomerCount: 3, contractCustomerCount: 2 }
            },
            {
                name: '海外部(历史)',
                userCount: 3,
                current: { phoneCount: 42, visitCount: 12, emailCount: 22, socialMediaCount: 65, newCustomerCount: 2, contractCustomerCount: 1 },
                previous: { phoneCount: 38, visitCount: 10, emailCount: 18, socialMediaCount: 58, newCustomerCount: 1, contractCustomerCount: 1 }
            }
        ],
        '3': [ // 混合模式
            {
                name: '销售一部',
                userCount: 5,
                note: '当前组织',
                current: { phoneCount: 85, visitCount: 25, emailCount: 45, socialMediaCount: 125, newCustomerCount: 5, contractCustomerCount: 3 },
                previous: { phoneCount: 75, visitCount: 20, emailCount: 38, socialMediaCount: 108, newCustomerCount: 3, contractCustomerCount: 2 }
            },
            {
                name: '销售二部',
                userCount: 4,
                note: '当前组织',
                current: { phoneCount: 72, visitCount: 20, emailCount: 38, socialMediaCount: 108, newCustomerCount: 4, contractCustomerCount: 2 },
                previous: { phoneCount: 65, visitCount: 18, emailCount: 32, socialMediaCount: 95, newCustomerCount: 3, contractCustomerCount: 2 }
            },
            {
                name: '销售三部(历史)',
                userCount: 4,
                note: '历史团队',
                current: { phoneCount: 68, visitCount: 18, emailCount: 35, socialMediaCount: 102, newCustomerCount: 4, contractCustomerCount: 2 },
                previous: { phoneCount: 60, visitCount: 15, emailCount: 30, socialMediaCount: 90, newCustomerCount: 3, contractCustomerCount: 2 }
            },
            {
                name: '海外部(历史)',
                userCount: 3,
                note: '历史团队',
                current: { phoneCount: 42, visitCount: 12, emailCount: 22, socialMediaCount: 65, newCustomerCount: 2, contractCustomerCount: 1 },
                previous: { phoneCount: 38, visitCount: 10, emailCount: 18, socialMediaCount: 58, newCustomerCount: 1, contractCustomerCount: 1 }
            }
        ]
    };

    return baseDepartmentData[statisticsMode] || baseDepartmentData['1'];
}

/**
 * 获取时间段信息
 */
function getPeriodInfo(timeRange, currentDate, customStartDate = null, customEndDate = null) {
    const now = new Date(currentDate);
    let currentStart, currentEnd, previousStart, previousEnd;

    switch(timeRange) {
        case 'custom':
            // 自定义时间段
            if (!customStartDate || !customEndDate) {
                // 如果没有提供自定义日期，默认使用本月
                currentStart = new Date(now.getFullYear(), now.getMonth(), 1);
                currentEnd = new Date(now);
            } else {
                currentStart = new Date(customStartDate);
                currentEnd = new Date(customEndDate);
            }

            // 计算对比时间段（相同长度的前一个时间段）
            const daysDiff = Math.ceil((currentEnd - currentStart) / (1000 * 60 * 60 * 24));
            previousEnd = new Date(currentStart);
            previousEnd.setDate(previousEnd.getDate() - 1);
            previousStart = new Date(previousEnd);
            previousStart.setDate(previousStart.getDate() - daysDiff);
            break;

        case 'thisWeek':
            // 本周：周一到今天
            const dayOfWeek = now.getDay() || 7; // 周日为7
            currentStart = new Date(now);
            currentStart.setDate(now.getDate() - dayOfWeek + 1);
            currentEnd = new Date(now);

            // 上周：完整的上周
            previousEnd = new Date(currentStart);
            previousEnd.setDate(previousEnd.getDate() - 1);
            previousStart = new Date(previousEnd);
            previousStart.setDate(previousEnd.getDate() - 6);
            break;

        case 'thisMonth':
            // 本月：1号到今天
            currentStart = new Date(now.getFullYear(), now.getMonth(), 1);
            currentEnd = new Date(now);

            // 上月：完整的上月
            previousStart = new Date(now.getFullYear(), now.getMonth() - 1, 1);
            previousEnd = new Date(now.getFullYear(), now.getMonth(), 0);
            break;

        case 'thisQuarter':
            // 本季度：季度开始到今天
            const quarter = Math.floor(now.getMonth() / 3);
            currentStart = new Date(now.getFullYear(), quarter * 3, 1);
            currentEnd = new Date(now);

            // 上季度：完整的上季度
            const prevQuarter = quarter === 0 ? 3 : quarter - 1;
            const prevYear = quarter === 0 ? now.getFullYear() - 1 : now.getFullYear();
            previousStart = new Date(prevYear, prevQuarter * 3, 1);
            previousEnd = new Date(prevYear, prevQuarter * 3 + 3, 0);
            break;

        case 'thisYear':
            // 本年：1月1日到今天
            currentStart = new Date(now.getFullYear(), 0, 1);
            currentEnd = new Date(now);

            // 去年：完整的去年
            previousStart = new Date(now.getFullYear() - 1, 0, 1);
            previousEnd = new Date(now.getFullYear() - 1, 11, 31);
            break;
    }

    return {
        current: {
            start: currentStart,
            end: currentEnd,
            days: Math.ceil((currentEnd - currentStart) / (1000 * 60 * 60 * 24)) + 1
        },
        previous: {
            start: previousStart,
            end: previousEnd,
            days: Math.ceil((previousEnd - previousStart) / (1000 * 60 * 60 * 24)) + 1
        }
    };
}

/**
 * 获取对比模式（直接使用用户选择的模式）
 */
function getComparisonMode(timeRange, periodInfo, userSelectedMode) {
    // 直接使用用户选择的对比模式
    return userSelectedMode || 'same_period';
}

/**
 * 生成基础数据
 */
function generateBaseData(periodInfo, comparisonMode) {
    // 模拟当前时间段的实际数据
    const currentData = {
        phoneCount: 156,
        visitCount: 45,
        emailPreciseCount: 89,
        emailBulkCount: 67,
        socialMediaCount: 234,
        replyCount: 123,
        demoCount: 23,
        newCustomerCount: 25,
        followCustomerCount: 78,
        contractCustomerCount: 12,
        reportCount: 89,
        onTimeRate: 85.4
    };

    // 模拟对比时间段的数据
    let previousData;

    switch(comparisonMode) {
        case 'same_period':
            // 同期对比：取上期相同天数的数据
            previousData = {
                phoneCount: Math.round(142 * periodInfo.current.days / periodInfo.previous.days),
                visitCount: Math.round(38 * periodInfo.current.days / periodInfo.previous.days),
                emailPreciseCount: Math.round(76 * periodInfo.current.days / periodInfo.previous.days),
                emailBulkCount: Math.round(58 * periodInfo.current.days / periodInfo.previous.days),
                socialMediaCount: Math.round(198 * periodInfo.current.days / periodInfo.previous.days),
                replyCount: Math.round(115 * periodInfo.current.days / periodInfo.previous.days),
                demoCount: Math.round(19 * periodInfo.current.days / periodInfo.previous.days),
                newCustomerCount: Math.round(18 * periodInfo.current.days / periodInfo.previous.days),
                followCustomerCount: Math.round(65 * periodInfo.current.days / periodInfo.previous.days),
                contractCustomerCount: Math.round(8 * periodInfo.current.days / periodInfo.previous.days),
                reportCount: Math.round(82 * periodInfo.current.days / periodInfo.previous.days),
                onTimeRate: 82.9
            };
            break;

        case 'daily_average':
            // 日均对比：当前日均 vs 上期日均
            const currentDaily = {};
            const previousDaily = {};

            Object.keys(currentData).forEach(key => {
                if (key !== 'onTimeRate') {
                    currentDaily[key] = Math.round(currentData[key] / periodInfo.current.days * 10) / 10;
                    previousDaily[key] = Math.round(142 / periodInfo.previous.days * 10) / 10; // 使用模拟数据
                } else {
                    currentDaily[key] = currentData[key];
                    previousDaily[key] = 82.9;
                }
            });

            return {
                summary: {
                    current: currentDaily,
                    previous: previousDaily
                }
            };

        case 'full_period':
        default:
            // 完整对比：当前累计 vs 上期完整
            previousData = {
                phoneCount: 142,
                visitCount: 38,
                emailPreciseCount: 76,
                emailBulkCount: 58,
                socialMediaCount: 198,
                replyCount: 115,
                demoCount: 19,
                newCustomerCount: 18,
                followCustomerCount: 65,
                contractCustomerCount: 8,
                reportCount: 82,
                onTimeRate: 82.9
            };
            break;
    }

    return {
        summary: {
            current: currentData,
            previous: previousData
        }
    };
}

/**
 * 获取对比说明
 */
function getComparisonNote(comparisonMode, periodInfo) {
    const currentDays = periodInfo.current.days;
    const previousDays = periodInfo.previous.days;
    const currentStart = periodInfo.current.start.toLocaleDateString();
    const currentEnd = periodInfo.current.end.toLocaleDateString();
    const previousStart = periodInfo.previous.start.toLocaleDateString();
    const previousEnd = periodInfo.previous.end.toLocaleDateString();

    switch(comparisonMode) {
        case 'same_period':
            return `同期对比模式：当前时间段${currentDays}天（${currentStart} ~ ${currentEnd}）与上期相同天数进行对比。适用于月初、季初等不完整时间段的对比分析。`;
        case 'daily_average':
            return `日均对比模式：当前日均工作量与上期日均工作量对比。消除时间段长度差异，更准确反映工作效率变化趋势。`;
        case 'full_period':
            return `完整对比模式：当前${currentDays}天累计数据（${currentStart} ~ ${currentEnd}）与上期完整${previousDays}天数据（${previousStart} ~ ${previousEnd}）对比。`;
        default:
            return '数据对比分析';
    }
}

/**
 * 更新时间段显示
 */
function updateTimePeriodDisplay(comparisonInfo, enableComparison) {
    const timePeriodDisplay = document.getElementById('timePeriodDisplay');
    const currentPeriodText = document.getElementById('currentPeriodText');
    const currentPeriodDays = document.getElementById('currentPeriodDays');
    const comparisonPeriodSection = document.getElementById('comparisonPeriodSection');
    const comparisonPeriodText = document.getElementById('comparisonPeriodText');
    const comparisonPeriodDays = document.getElementById('comparisonPeriodDays');

    if (!timePeriodDisplay || !comparisonInfo) return;

    // 格式化日期显示
    const formatDateRange = (start, end) => {
        const startDate = new Date(start);
        const endDate = new Date(end);
        return `${startDate.getFullYear()}年${startDate.getMonth() + 1}月${startDate.getDate()}日 - ${endDate.getFullYear()}年${endDate.getMonth() + 1}月${endDate.getDate()}日`;
    };

    // 更新当前时间段
    if (currentPeriodText && currentPeriodDays) {
        const currentRange = formatDateRange(comparisonInfo.currentPeriod.start, comparisonInfo.currentPeriod.end);
        currentPeriodText.textContent = `当前统计时间段 (${currentRange})`;
        currentPeriodDays.textContent = `共${comparisonInfo.currentPeriod.days}天`;
    }

    // 更新对比时间段
    if (enableComparison && comparisonPeriodSection && comparisonPeriodText && comparisonPeriodDays) {
        comparisonPeriodSection.style.display = 'block';
        const comparisonRange = formatDateRange(comparisonInfo.previousPeriod.start, comparisonInfo.previousPeriod.end);

        // 根据对比模式设置不同的标题
        let comparisonTitle = '对比时间段';
        switch(comparisonInfo.mode) {
            case 'same_period':
                comparisonTitle = '同期对比时间段';
                break;
            case 'daily_average':
                comparisonTitle = '日均对比时间段';
                break;
            case 'full_period':
                comparisonTitle = '完整对比时间段';
                break;
        }

        comparisonPeriodText.textContent = `${comparisonTitle} (${comparisonRange})`;
        comparisonPeriodDays.textContent = `共${comparisonInfo.previousPeriod.days}天`;

        // 显示VS分隔符
        const periodDivider = timePeriodDisplay.querySelector('.period-divider');
        if (periodDivider) {
            periodDivider.style.display = 'block';
        }
    } else {
        // 隐藏对比时间段
        if (comparisonPeriodSection) {
            comparisonPeriodSection.style.display = 'none';
        }

        // 隐藏VS分隔符
        const periodDivider = timePeriodDisplay.querySelector('.period-divider');
        if (periodDivider) {
            periodDivider.style.display = 'none';
        }
    }

    // 添加淡入动画
    timePeriodDisplay.style.opacity = '0';
    setTimeout(() => {
        timePeriodDisplay.style.opacity = '1';
        timePeriodDisplay.style.transition = 'opacity 0.3s ease';
    }, 100);
}

/**
 * 更新对比说明显示
 */
function updateComparisonInfo(comparisonInfo, enableComparison) {
    const comparisonInfoElement = document.getElementById('comparisonInfo');
    const comparisonNoteElement = document.getElementById('comparisonNote');

    if (!comparisonInfoElement || !comparisonNoteElement) return;

    if (enableComparison && comparisonInfo) {
        comparisonInfoElement.style.display = 'block';
        comparisonNoteElement.textContent = comparisonInfo.note;

        // 添加淡入动画
        comparisonInfoElement.style.opacity = '0';
        setTimeout(() => {
            comparisonInfoElement.style.opacity = '1';
            comparisonInfoElement.style.transition = 'opacity 0.3s ease';
        }, 100);
    } else {
        comparisonInfoElement.style.display = 'none';
    }
}
