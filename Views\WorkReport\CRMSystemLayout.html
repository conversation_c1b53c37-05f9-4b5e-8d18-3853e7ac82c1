<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>许昌慧聪CRM - 工作回顾</title>
    <link rel="stylesheet" href="css/crm-layout.css">
    <link rel="stylesheet" href="css/work-review-integrated.css">
</head>
<body>
    <!-- 顶部导航栏 -->
    <header class="top-navbar">
        <div class="navbar-left">
            <div class="logo-section">
                <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='%23ffffff'%3E%3Cpath d='M12 2L2 7v10c0 5.55 3.84 9.74 9 11 5.16-1.26 9-5.45 9-11V7l-10-5z'/%3E%3C/svg%3E" alt="Logo" class="logo-icon">
                <span class="system-title">许昌慧聪CRM</span>
            </div>
        </div>
        <nav class="navbar-center">
            <a href="#" class="nav-item active">工作台</a>
            <a href="#" class="nav-item">工作流</a>
            <a href="#" class="nav-item">个人中心</a>
        </nav>
        <div class="navbar-right">
            <div class="user-info">
                <span class="user-name">张三</span>
                <div class="user-avatar">张</div>
                <div class="dropdown-arrow">▼</div>
            </div>
        </div>
    </header>

    <!-- 主体容器 -->
    <div class="main-container">
        <!-- 左侧菜单栏 -->
        <aside class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <button class="sidebar-toggle" id="sidebarToggle">
                    <span class="toggle-icon">☰</span>
                </button>
            </div>
            <nav class="sidebar-nav">
                <ul class="nav-menu">
                    <li class="nav-item">
                        <a href="#" class="nav-link">
                            <span class="nav-icon">🏠</span>
                            <span class="nav-text">首页</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link">
                            <span class="nav-icon">📊</span>
                            <span class="nav-text">我的CRM</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link">
                            <span class="nav-icon">📈</span>
                            <span class="nav-text">CRM统计</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link">
                            <span class="nav-icon">👥</span>
                            <span class="nav-text">客户管理</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link">
                            <span class="nav-icon">📋</span>
                            <span class="nav-text">合同管理</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link">
                            <span class="nav-icon">💰</span>
                            <span class="nav-text">财务管理</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link">
                            <span class="nav-icon">📦</span>
                            <span class="nav-text">产品管理</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link">
                            <span class="nav-icon">🔧</span>
                            <span class="nav-text">服务管理</span>
                        </a>
                    </li>
                    <li class="nav-item has-submenu">
                        <a href="#" class="nav-link">
                            <span class="nav-icon">📝</span>
                            <span class="nav-text">工作报告</span>
                            <span class="submenu-arrow">▶</span>
                        </a>
                        <ul class="submenu">
                            <li class="submenu-item">
                                <a href="#" class="submenu-link">日报管理</a>
                            </li>
                            <li class="submenu-item">
                                <a href="#" class="submenu-link active">工作回顾</a>
                            </li>
                            <li class="submenu-item">
                                <a href="#" class="submenu-link">绩效统计</a>
                            </li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link">
                            <span class="nav-icon">⚙️</span>
                            <span class="nav-text">系统设置</span>
                        </a>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 面包屑导航 -->
            <div class="breadcrumb">
                <span class="breadcrumb-item">
                    <a href="#">首页</a>
                </span>
                <span class="breadcrumb-separator">></span>
                <span class="breadcrumb-item">
                    <a href="#">工作报告</a>
                </span>
                <span class="breadcrumb-separator">></span>
                <span class="breadcrumb-item current">工作回顾</span>
            </div>

            <!-- 页面标题区域 -->
            <div class="page-header">
                <div class="page-title-section">
                    <h1 class="page-title">📊 工作回顾</h1>
                    <p class="page-subtitle">基于日报数据的团队工作量统计分析，支持多维度对比和趋势分析</p>
                </div>
                <div class="page-actions">
                    <button class="btn btn-secondary">
                        <span class="btn-icon">📤</span>
                        导出报告
                    </button>
                    <button class="btn btn-primary">
                        <span class="btn-icon">🔄</span>
                        刷新数据
                    </button>
                </div>
            </div>

            <!-- 工作回顾模块内容 -->
            <div class="work-review-container">
                <!-- 筛选条件卡片 -->
                <div class="filter-card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <span class="card-icon">🔍</span>
                            筛选条件
                        </h3>
                        <button class="btn btn-primary btn-sm" onclick="loadData()">
                            <span class="btn-icon">🔍</span>
                            查询
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="filter-grid">
                            <div class="filter-group">
                                <label class="filter-label">时间范围</label>
                                <select class="form-select" id="timeRange">
                                    <option value="thisWeek">本周</option>
                                    <option value="thisMonth" selected>本月</option>
                                    <option value="thisQuarter">本季度</option>
                                    <option value="thisYear">本年</option>
                                    <option value="custom">自定义时间段</option>
                                </select>
                            </div>
                            <div class="filter-group custom-date-group" id="customDateGroup" style="display: none;">
                                <label class="filter-label">开始日期</label>
                                <input type="date" class="form-control" id="customStartDate">
                            </div>
                            <div class="filter-group custom-date-group" id="customDateGroup2" style="display: none;">
                                <label class="filter-label">结束日期</label>
                                <input type="date" class="form-control" id="customEndDate">
                            </div>
                            <div class="filter-group">
                                <label class="filter-label">部门</label>
                                <select class="form-select" id="department">
                                    <option value="">全部部门</option>
                                    <option value="sales1">销售一部</option>
                                    <option value="sales2">销售二部</option>
                                    <option value="sales3">销售三部</option>
                                    <option value="overseas">海外部</option>
                                </select>
                            </div>
                            <div class="filter-group">
                                <label class="filter-label">人员</label>
                                <select class="form-select" id="personnel">
                                    <option value="">全部人员</option>
                                    <option value="user1">张三</option>
                                    <option value="user2">李四</option>
                                    <option value="user3">王五</option>
                                </select>
                            </div>
                            <div class="filter-group">
                                <label class="filter-label">统计模式</label>
                                <select class="form-select" id="statisticsMode">
                                    <option value="1">按当前组织架构</option>
                                    <option value="2">按报告提交时团队</option>
                                    <option value="3">混合模式</option>
                                </select>
                            </div>
                            <div class="filter-group">
                                <label class="filter-label">对比模式</label>
                                <select class="form-select" id="comparisonMode">
                                    <option value="same_period">同期对比</option>
                                    <option value="daily_average">日均对比</option>
                                    <option value="full_period">完整对比</option>
                                </select>
                            </div>
                            <div class="filter-group checkbox-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" id="enableComparison" checked>
                                    <span class="checkbox-text">启用对比</span>
                                </label>
                            </div>
                            <div class="filter-group checkbox-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" id="onlyDirect">
                                    <span class="checkbox-text">仅直属</span>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 对比说明 -->
                <div class="comparison-info" id="comparisonInfo" style="display: none;">
                    <div class="info-card">
                        <div class="info-icon">ℹ️</div>
                        <div class="info-content">
                            <div class="info-title">对比说明</div>
                            <div class="info-text" id="comparisonNote"></div>
                        </div>
                    </div>
                </div>

                <!-- 统计概览卡片 -->
                <div class="overview-cards">
                    <div class="overview-card">
                        <div class="card-icon-wrapper blue">
                            <span class="card-icon">📞</span>
                        </div>
                        <div class="card-content">
                            <div class="card-value">156</div>
                            <div class="card-label">电话量</div>
                            <div class="card-trend positive">+9.9%</div>
                        </div>
                    </div>
                    <div class="overview-card">
                        <div class="card-icon-wrapper green">
                            <span class="card-icon">🤝</span>
                        </div>
                        <div class="card-content">
                            <div class="card-value">45</div>
                            <div class="card-label">拜访量</div>
                            <div class="card-trend positive">+18.4%</div>
                        </div>
                    </div>
                    <div class="overview-card">
                        <div class="card-icon-wrapper orange">
                            <span class="card-icon">📧</span>
                        </div>
                        <div class="card-content">
                            <div class="card-value">234</div>
                            <div class="card-label">社媒量</div>
                            <div class="card-trend positive">+18.2%</div>
                        </div>
                    </div>
                    <div class="overview-card">
                        <div class="card-icon-wrapper purple">
                            <span class="card-icon">👥</span>
                        </div>
                        <div class="card-content">
                            <div class="card-value">25</div>
                            <div class="card-label">新增客户</div>
                            <div class="card-trend positive">+38.9%</div>
                        </div>
                    </div>
                </div>

                <!-- 选项卡导航 -->
                <div class="tab-container">
                    <div class="tab-nav">
                        <button class="tab-item active" onclick="switchTab('summary')">
                            <span class="tab-icon">📈</span>
                            汇总统计
                        </button>
                        <button class="tab-item" onclick="switchTab('personnel')">
                            <span class="tab-icon">👥</span>
                            人员详细
                        </button>
                        <button class="tab-item" onclick="switchTab('department')">
                            <span class="tab-icon">🏢</span>
                            部门统计
                        </button>
                    </div>
                </div>

                <!-- 表格内容区域 -->
                <div id="tableContainer" class="table-container-wrapper">
                    <!-- 表格内容将通过JavaScript动态加载 -->
                </div>
            </div>
        </main>
    </div>

    <!-- 引入JavaScript文件 -->
    <script src="js/crm-layout.js"></script>
    <script src="js/work-review-main.js"></script>
    <script src="js/work-review-tables.js"></script>
</body>
</html>
