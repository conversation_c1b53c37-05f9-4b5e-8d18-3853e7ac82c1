/* 工作回顾模块集成样式 */

.work-review-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

/* 筛选条件卡片 */
.filter-card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    overflow: hidden;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.card-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 16px;
    font-weight: 600;
    color: #2c3e50;
    margin: 0;
}

.card-icon {
    font-size: 16px;
}

.card-body {
    padding: 20px;
}

.filter-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    align-items: end;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.filter-group.checkbox-group {
    flex-direction: row;
    align-items: center;
    gap: 0;
}

.filter-label {
    font-size: 13px;
    font-weight: 500;
    color: #555;
    margin-bottom: 4px;
}

.form-select {
    height: 36px;
    padding: 0 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    background: white;
    color: #333;
    font-size: 14px;
    transition: all 0.3s ease;
    appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 12 12'%3E%3Cpath fill='%23666' d='M6 8.5L1.5 4 2.5 3l3.5 3.5L9.5 3l1 1z'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 12px center;
    padding-right: 36px;
}

.form-select:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.form-control {
    height: 36px;
    padding: 0 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    background: white;
    color: #333;
    font-size: 14px;
    transition: all 0.3s ease;
}

.form-control:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.custom-date-group {
    animation: slideDown 0.3s ease;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    font-size: 14px;
    color: #555;
}

.checkbox-label input[type="checkbox"] {
    width: 16px;
    height: 16px;
    accent-color: #3498db;
}

/* 对比说明 */
.comparison-info {
    margin-bottom: 20px;
}

.info-card {
    background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
    border: 1px solid #bbdefb;
    border-radius: 8px;
    padding: 16px;
    display: flex;
    align-items: center;
    gap: 12px;
}

.info-icon {
    font-size: 20px;
    opacity: 0.8;
}

.info-content {
    flex: 1;
}

.info-title {
    font-size: 14px;
    font-weight: 600;
    color: #1976d2;
    margin-bottom: 4px;
}

.info-text {
    font-size: 13px;
    color: #424242;
    line-height: 1.4;
}

/* 统计概览卡片 */
.overview-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: 16px;
    margin-bottom: 20px;
}

.overview-card {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    display: flex;
    align-items: center;
    gap: 16px;
    transition: all 0.3s ease;
}

.overview-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.card-icon-wrapper {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
}

.card-icon-wrapper.blue {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
}

.card-icon-wrapper.green {
    background: linear-gradient(135deg, #27ae60, #229954);
    color: white;
}

.card-icon-wrapper.orange {
    background: linear-gradient(135deg, #f39c12, #e67e22);
    color: white;
}

.card-icon-wrapper.purple {
    background: linear-gradient(135deg, #9b59b6, #8e44ad);
    color: white;
}

.card-content {
    flex: 1;
}

.card-value {
    font-size: 24px;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 4px;
}

.card-label {
    font-size: 13px;
    color: #7f8c8d;
    margin-bottom: 4px;
}

.card-trend {
    font-size: 12px;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 4px;
    display: inline-block;
}

.card-trend.positive {
    background: #d5f4e6;
    color: #27ae60;
}

.card-trend.negative {
    background: #fdeaea;
    color: #e74c3c;
}

/* 选项卡导航 */
.tab-container {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    overflow: hidden;
}

.tab-nav {
    display: flex;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.tab-item {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 16px 20px;
    background: none;
    border: none;
    color: #666;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.tab-item:hover {
    background: #e9ecef;
    color: #3498db;
}

.tab-item.active {
    background: white;
    color: #3498db;
    border-bottom: 2px solid #3498db;
}

.tab-icon {
    font-size: 16px;
}

/* 表格容器 */
.table-container-wrapper {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    overflow: hidden;
}

.statistics-card {
    background: white;
    border-radius: 8px;
    overflow: hidden;
}

.statistics-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.statistics-body {
    padding: 0;
}

.table-container {
    max-height: 600px;
    overflow: auto;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 13px;
}

.data-table th,
.data-table td {
    padding: 12px 8px;
    text-align: center;
    border-bottom: 1px solid #e9ecef;
    vertical-align: middle;
}

.data-table th {
    background: #f8f9fa;
    color: #555;
    font-weight: 600;
    position: sticky;
    top: 0;
    z-index: 10;
    border-bottom: 2px solid #e9ecef;
}

.data-table tbody tr:hover {
    background: #f8f9fa;
}

.data-table tbody tr:last-child td {
    border-bottom: none;
}

.number-cell {
    font-weight: 600;
    color: #2c3e50;
}

.change-cell {
    font-weight: 600;
}

.percentage-cell {
    font-size: 12px;
    font-weight: 600;
}

.trend-up {
    color: #27ae60;
}

.trend-down {
    color: #e74c3c;
}

.trend-equal {
    color: #7f8c8d;
}

/* 加载和无数据状态 */
.loading,
.no-data {
    text-align: center;
    padding: 60px 20px;
    color: #7f8c8d;
    font-size: 14px;
}

.loading::before {
    content: "⏳";
    display: block;
    font-size: 32px;
    margin-bottom: 16px;
}

.no-data::before {
    content: "📊";
    display: block;
    font-size: 32px;
    margin-bottom: 16px;
    opacity: 0.5;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .filter-grid {
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    }
    
    .overview-cards {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
}

@media (max-width: 768px) {
    .filter-grid {
        grid-template-columns: 1fr;
    }
    
    .overview-cards {
        grid-template-columns: 1fr;
    }
    
    .tab-nav {
        flex-direction: column;
    }
    
    .tab-item {
        justify-content: flex-start;
        padding: 12px 20px;
    }
    
    .data-table {
        font-size: 12px;
    }
    
    .data-table th,
    .data-table td {
        padding: 8px 4px;
    }
}

/* 表格滚动条样式 */
.table-container::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

.table-container::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.table-container::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.table-container::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
